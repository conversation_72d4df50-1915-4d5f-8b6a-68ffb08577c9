import type { Meta } from '@storybook/react-vite';
import React from 'react';
import { IconButton } from '../../Button/IconButton/IconButton';
import { EllipsisVerticalIcon, PlusIcon } from '../../Icons/outline';
import type { NodeModel } from '../Tree';
import { createItem } from '../tree-utils';

import { TreeItem } from './TreeItem';
import { TreeItemActions } from './TreeItemActions';
import { TreeItemContent } from './TreeItemContent';

type NodeData = { name: string };
const node: NodeModel<NodeData> = createItem({
  id: 1,
  parent: 0,
  text: 'Folder 1',
  data: {
    name: 'Folder 1-1',
  },
});

export default {
  title: 'Layout and Organization/Tree/TreeItem',
  component: TreeItem,
} as Meta<typeof TreeItem>;

export const Default = {
  args: {
    node,
    params: {
      checked: false,
      depth: 0,
      draggable: true,
      hasChild: false,
      isOpen: true,
      onCheck: () => {},
      onToggle: () => {},
    },
  },
};

export const Checked = {
  args: {
    ...Default.args,
    params: {
      ...Default.args?.params!,
      checked: true,
    },
  },
};

export const CustomContent = {
  args: {
    ...Default.args,
    children: [
      <TreeItemContent key={0}>
        <span>{node.data?.name}</span>
        <span>(this is custom)</span>
      </TreeItemContent>,
    ],
  },
};

export const CustomActions = {
  args: {
    ...Default.args,
    children: [
      <TreeItemContent key={0}>
        <span>{node.data?.name}</span>
        <span>(this is custom)</span>
      </TreeItemContent>,
      <TreeItemActions key={1}>
        <IconButton variant="text" color="secondary" icon={EllipsisVerticalIcon} size="xs" />
        <IconButton variant="text" color="secondary" icon={PlusIcon} size="xs" />
      </TreeItemActions>,
    ],
  },
};

export const LongName = {
  args: {
    ...Default.args,
    children: [
      <TreeItemContent key={0}>
        This is the beginning of a very long name: Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam eu
        elit sed justo mattis vestibulum ac sed risus. In in congue neque. Pellentesque vitae maximus sem. Curabitur
        semper efficitur mauris, ac bibendum leo interdum et. Nulla tincidunt purus vitae magna faucibus aliquam. Aenean
        nec iaculis leo. Nam eget tristique dui. Etiam dapibus libero pellentesque neque venenatis elementum. Ut tempor
        mauris ullamcorper, consectetur odio nec, consectetur dolor. Fusce feugiat nunc tristique, viverra ligula ut,
        sollicitudin arcu. Donec purus eros, euismod eu efficitur eget, porttitor vel sapien. Pellentesque leo risus,
        commodo sit amet justo eu, viverra pellentesque velit. Aenean euismod aliquam eros non tristique. Ut scelerisque
        sapien nulla, et pretium augue pretium et.
      </TreeItemContent>,
    ],
  },
};
