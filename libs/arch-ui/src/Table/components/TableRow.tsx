import React from 'react';
import { cn } from '../../utils/classes';

export interface TableRowProps extends React.ComponentProps<'tr'> {
  /**
   * alternating striped rows
   */
  striped?: boolean;
  highlighted?: boolean;
  /**
   * Custom highlight color. Defaults to 'success' (green)
   */
  highlightColor?: 'success' | 'primary' | 'info' | 'warning' | 'danger';
}

const highlightColorClasses = {
  success: '!bg-success-subtle',
  primary: '!bg-brand-subtle',
  info: '!bg-info-subtle',
  warning: '!bg-warning-subtle',
  danger: '!bg-danger-subtle',
};

export const TableRow = React.forwardRef<HTMLTableRowElement, TableRowProps>(
  ({ striped, children, className, highlighted, highlightColor = 'primary', ...props }, ref) => {
    const highlightClass = highlighted ? highlightColorClasses[highlightColor] : '';
    const classes = cn({ 'odd:bg-white even:bg-gray-50': striped }, highlightClass, className);

    return (
      <tr className={classes} {...props} ref={ref}>
        {children}
      </tr>
    );
  }
);
