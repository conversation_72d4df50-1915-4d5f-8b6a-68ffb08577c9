import type { Meta } from '@storybook/react-vite';
import { TreeSelector } from './TreeSelector';

const exampleTree = {
  id: '1',
  name: 'Whole Wide',
  shortCode: 'ROOT',
  children: [
    {
      id: '1',
      parentId: null,
      name: 'Project Wide',
      shortCode: 'PROJ',
      hasChildren: true,
    },
    {
      id: '2',
      parentId: '1',
      name: 'Battersea Power Station',
      shortCode: 'BPS',
      hasChildren: true,
    },
    {
      id: '3',
      parentId: '2',
      name: 'Concourse',
      shortCode: 'CON',
    },
    {
      id: '4',
      parentId: '21',
      name: 'Car Park',
      shortCode: 'CPK',
      hasChildren: true,
    },
    {
      id: '5',
      parentId: '4',
      name: 'Level 1',
      shortCode: 'LV1',
    },
    {
      id: '6',
      parentId: '1',
      name: 'Nine Elms',
      shortCode: 'NELMS',
      hasChildren: true,
    },
    {
      id: '7',
      parentId: '6',
      name: 'Ticket Office',
      shortCode: 'TICK',
      hasChildren: true,
    },
    {
      id: '8',
      parentId: '7',
      name: 'Front',
      shortCode: 'FRO',
    },
    {
      id: '9',
      parentId: '7',
      name: 'Back',
      shortCode: 'BAC',
    },
    {
      id: '10',
      parentId: '4',
      name: 'Level 2',
      shortCode: 'LV2',
    },
    {
      id: '11',
      parentId: '4',
      name: 'Level 3',
      shortCode: 'LV3',
    },
    {
      id: '12',
      parentId: '4',
      name: 'Level 4',
      shortCode: 'LV4',
    },
    {
      id: '13',
      parentId: '4',
      name: 'Level 5',
      shortCode: 'LV5',
    },
    {
      id: '14',
      parentId: '4',
      name: 'Level 6',
      shortCode: 'LV6',
    },
    {
      id: '15',
      parentId: '4',
      name: 'Level 7',
      shortCode: 'LV7',
    },
    {
      id: '16',
      parentId: '4',
      name: 'Level 8',
      shortCode: 'LV8',
    },
    {
      id: '17',
      parentId: '4',
      name: 'Level 9',
      shortCode: 'LV9',
    },
    {
      id: '18',
      parentId: '4',
      name: 'Level 10',
      shortCode: 'LV10',
    },
    {
      id: '19',
      parentId: '4',
      name: 'Level 11',
      shortCode: 'LV11',
      hasChildren: true,
    },
    {
      id: '20',
      parentId: '19',
      name: 'East',
      shortCode: 'EAS',
    },
    {
      id: '21',
      parentId: '2',
      name: 'Outside',
      shortCode: 'OUT',
      hasChildren: true,
    },
  ],
};

export default {
  title: 'Layout and Organisation/Tree Selector',
  component: TreeSelector,
} as Meta<typeof TreeSelector>;

export const Standard = {
  args: {
    nodes: exampleTree.children,
    rootNodeId: '1',
    searchFieldPlaceholder: 'Search',
  },
};

export const LongName = {
  args: {
    nodes: [
      {
        id: '0',
        parentId: '1',
        name: 'Lorem ipsum dolor sit amet, officia excepteur ex fugiat reprehenderit enim labore culpa sint ad nisi Lorempariatur mollit ex esse exercitation amet. Nisi anim cupidatat excepteur officia. Reprehenderit nostrudnostrud ipsum Lorem est aliquip amet voluptate voluptate dolor minim nulla est proident. Nostrud officiapariatur ut officia. Sit irure elit esse ea',
        shortCode: 'LOREM',
        hasChildren: true,
      },
      ...exampleTree.children,
    ],
    rootNodeId: '1',
    searchFieldPlaceholder: 'Search',
  },
};
