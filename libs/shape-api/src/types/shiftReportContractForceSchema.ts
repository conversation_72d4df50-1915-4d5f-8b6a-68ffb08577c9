/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { ShiftReportResourceAllocationSchema } from './shiftReportResourceAllocationSchema';

export type ShiftReportContractForceSchema = {
  /**
   * @type array
   */
  activities: ShiftReportResourceAllocationSchema[];
  /**
   * @type string
   */
  comment: string | null;
  /**
   * @type integer
   */
  documentCount: number;
  /**
   * @type array
   */
  downTimes: ShiftReportResourceAllocationSchema[];
  /**
   * @type number, float
   */
  hours: number | null;
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type string
   */
  name: string | null;
  /**
   * @type string
   */
  organisation: string | null;
  /**
   * @type string, uuid
   */
  organisationResourceId: string | null;
  /**
   * @type string, uuid
   */
  personResourceId: string | null;
  /**
   * @type string
   */
  role: string | null;
  /**
   * @type string, uuid
   */
  roleResourceId: string | null;
  /**
   * @type integer
   */
  teamMemberId: number;
};
