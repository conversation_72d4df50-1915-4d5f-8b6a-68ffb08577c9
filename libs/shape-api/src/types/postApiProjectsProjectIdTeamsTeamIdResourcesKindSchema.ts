/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ResourceKindSchema } from './resourceKindSchema';
import type { ResourceSchema } from './resourceSchema';

export type PostApiProjectsProjectIdTeamsTeamIdResourcesKindPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  team_id: string;
  /**
   * @type string
   */
  kind: ResourceKindSchema;
};

/**
 * @description Resource created
 */
export type PostApiProjectsProjectIdTeamsTeamIdResourcesKind201Schema = ResourceSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdTeamsTeamIdResourcesKind401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorized
 */
export type PostApiProjectsProjectIdTeamsTeamIdResourcesKind403Schema = unknown;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdTeamsTeamIdResourcesKind404Schema = unknown;

/**
 * @description Create failed
 */
export type PostApiProjectsProjectIdTeamsTeamIdResourcesKind422Schema = ErrorSchema;

export type PostApiProjectsProjectIdTeamsTeamIdResourcesKindMutationRequestSchema = {
  /**
   * @type string
   */
  name: string;
  /**
   * @type boolean | undefined
   */
  temporary?: boolean;
};

export type PostApiProjectsProjectIdTeamsTeamIdResourcesKindMutationResponseSchema =
  PostApiProjectsProjectIdTeamsTeamIdResourcesKind201Schema;

export type PostApiProjectsProjectIdTeamsTeamIdResourcesKindSchemaMutation = {
  Response: PostApiProjectsProjectIdTeamsTeamIdResourcesKind201Schema;
  Request: PostApiProjectsProjectIdTeamsTeamIdResourcesKindMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdTeamsTeamIdResourcesKindPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdTeamsTeamIdResourcesKind401Schema
    | PostApiProjectsProjectIdTeamsTeamIdResourcesKind403Schema
    | PostApiProjectsProjectIdTeamsTeamIdResourcesKind404Schema
    | PostApiProjectsProjectIdTeamsTeamIdResourcesKind422Schema;
};
