/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { ShiftActivitySchema } from './shiftActivitySchema';
import type { WeeklyWorkPlanActivityStatusesSchema } from './weeklyWorkPlanActivityStatusesSchema';
import type { WeeklyWorkPlanActivityVarianceCategoriesSchema } from './weeklyWorkPlanActivityVarianceCategoriesSchema';

export type WeeklyWorkPlanActivitySchema = {
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type string
   */
  comment: string | null;
  /**
   * @type number, float
   */
  expectedPercentageCompleted: number | null;
  /**
   * @type array
   */
  extendedLoggedDays: {
    /**
     * @type string, date
     */
    date: string;
    /**
     * @type number, float
     */
    delta: number | null;
    /**
     * @type integer
     */
    numberOfLogs: number;
  }[];
  /**
   * @type array
   */
  loggedDays: string[];
  /**
   * @type number, float
   */
  percentageCompleted: number | null;
  /**
   * @type array
   */
  scheduledDays: string[];
  /**
   * @type object
   */
  shiftActivity: ShiftActivitySchema;
  /**
   * @type string
   */
  status: WeeklyWorkPlanActivityStatusesSchema;
  varianceCategory: WeeklyWorkPlanActivityVarianceCategoriesSchema | null;
  /**
   * @type string
   */
  varianceRecoveryMitigationMeasures: string | null;
  /**
   * @type string
   */
  varianceRemarks: string | null;
};
