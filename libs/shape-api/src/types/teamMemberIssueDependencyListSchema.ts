/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { TeamMemberIssueDependencySchema } from './teamMemberIssueDependencySchema';

export type TeamMemberIssueDependencyListSchema = {
  /**
   * @type array
   */
  observedIssues: TeamMemberIssueDependencySchema[];
  /**
   * @type array
   */
  assignmentRequestedIssues: TeamMemberIssueDependencySchema[];
  /**
   * @type array
   */
  assignedIssues: TeamMemberIssueDependencySchema[];
  /**
   * @type array
   */
  issuesAsApprover: TeamMemberIssueDependencySchema[];
};
