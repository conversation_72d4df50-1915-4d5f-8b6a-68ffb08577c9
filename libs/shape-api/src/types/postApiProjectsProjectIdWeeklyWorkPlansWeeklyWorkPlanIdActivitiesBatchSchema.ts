/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { OneOrManyIntegerNullableSchema } from './oneOrManyIntegerNullableSchema';
import type { OneOrManyUuidNullableSchema } from './oneOrManyUuidNullableSchema';
import type { OneOrManyUuidSchema } from './oneOrManyUuidSchema';
import type { ShiftActivityStatusSchema } from './shiftActivityStatusSchema';

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesBatchPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  weekly_work_plan_id: string;
};

/**
 * @description Weekly work plan activities created
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesBatch204Schema = unknown;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesBatch401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesBatch403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesBatch404Schema = unknown;

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesBatchMutationRequestSchema = {
  /**
   * @type object
   */
  shift_activities: {
    id?: OneOrManyUuidSchema;
    /**
     * @type boolean | undefined
     */
    archived?: boolean;
    status?: ShiftActivityStatusSchema[] | ShiftActivityStatusSchema;
    assigned_team_member_id?: OneOrManyIntegerNullableSchema;
    location_id?: OneOrManyUuidNullableSchema;
    /**
     * @type boolean | undefined
     */
    critical?: boolean;
    owner_id?: OneOrManyIntegerNullableSchema;
    /**
     * @type string | undefined, date-time
     */
    planned_start_date_start?: string;
    /**
     * @type string | undefined, date-time
     */
    planned_start_date_end?: string;
    /**
     * @type string | undefined, date-time
     */
    planned_end_date_start?: string;
    /**
     * @type string | undefined, date-time
     */
    planned_end_date_end?: string;
    /**
     * @type string | undefined, date-time
     */
    expected_finish_date_start?: string;
    /**
     * @type string | undefined, date-time
     */
    expected_finish_date_end?: string;
    /**
     * @type string | undefined, date-time
     */
    actual_start_date_start?: string;
    /**
     * @type string | undefined, date-time
     */
    actual_start_date_end?: string;
    /**
     * @type string | undefined, date-time
     */
    actual_end_date_start?: string;
    /**
     * @type string | undefined, date-time
     */
    actual_end_date_end?: string;
    /**
     * @type boolean | undefined
     */
    ready?: boolean;
    /**
     * @type string | undefined
     */
    search?: string;
  };
};

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesBatchMutationResponseSchema =
  PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesBatch204Schema;

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesBatchSchemaMutation = {
  Response: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesBatch204Schema;
  Request: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesBatchMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesBatchPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesBatch401Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesBatch403Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesBatch404Schema;
};
