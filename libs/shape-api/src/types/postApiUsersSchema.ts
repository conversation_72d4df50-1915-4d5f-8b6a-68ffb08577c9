/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationStrategyTypeSchema } from './authenticationStrategyTypeSchema';
import type { ErrorSchema } from './errorSchema';
import type { UserSchema } from './userSchema';

/**
 * @description User created
 */
export type PostApiUsers201Schema = UserSchema;

/**
 * @description Failed request
 */
export type PostApiUsers422Schema = ErrorSchema;

export type PostApiUsersMutationRequestSchema = {
  /**
   * @type object
   */
  user: {
    /**
     * @type string
     */
    email: string;
    /**
     * @type string | undefined
     */
    password?: string;
    /**
     * @type string
     */
    first_name: string;
    /**
     * @type string
     */
    last_name: string;
  };
  /**
   * @type string, uuid
   */
  accepted_eua: string;
  /**
   * @type string | undefined, uri
   */
  referrer_path?: string;
  /**
   * @type string | undefined
   */
  referrer_team_join_token?: string;
  /**
   * @type string | undefined
   */
  strategy?: AuthenticationStrategyTypeSchema;
  /**
   * @type string | undefined
   */
  token?: string;
  /**
   * @type string | undefined
   */
  confirmation_token?: string;
  /**
   * @type object | undefined
   */
  utm_params?: {
    /**
     * @type string
     */
    utm_campaign?: string | null;
    /**
     * @type string
     */
    utm_content?: string | null;
    /**
     * @type string
     */
    utm_id?: string | null;
    /**
     * @type string
     */
    utm_medium?: string | null;
    /**
     * @type string
     */
    utm_source?: string | null;
    /**
     * @type string
     */
    utm_term?: string | null;
  };
};

export type PostApiUsersMutationResponseSchema = PostApiUsers201Schema;

export type PostApiUsersSchemaMutation = {
  Response: PostApiUsers201Schema;
  Request: PostApiUsersMutationRequestSchema;
  Errors: PostApiUsers422Schema;
};
