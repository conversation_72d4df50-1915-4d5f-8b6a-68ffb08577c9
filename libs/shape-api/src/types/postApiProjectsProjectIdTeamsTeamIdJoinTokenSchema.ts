/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { TeamJoinTokenSchema } from './teamJoinTokenSchema';

export type PostApiProjectsProjectIdTeamsTeamIdJoinTokenPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  team_id: string;
};

/**
 * @description Team join token created
 */
export type PostApiProjectsProjectIdTeamsTeamIdJoinToken201Schema = TeamJoinTokenSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdTeamsTeamIdJoinToken401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdTeamsTeamIdJoinToken403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdTeamsTeamIdJoinToken404Schema = unknown;

/**
 * @description Create failed
 */
export type PostApiProjectsProjectIdTeamsTeamIdJoinToken422Schema = ErrorSchema;

export type PostApiProjectsProjectIdTeamsTeamIdJoinTokenMutationRequestSchema = {
  /**
   * @type string, date-time
   */
  expires_at: string;
  /**
   * @type integer
   */
  usage_limit: number;
};

export type PostApiProjectsProjectIdTeamsTeamIdJoinTokenMutationResponseSchema =
  PostApiProjectsProjectIdTeamsTeamIdJoinToken201Schema;

export type PostApiProjectsProjectIdTeamsTeamIdJoinTokenSchemaMutation = {
  Response: PostApiProjectsProjectIdTeamsTeamIdJoinToken201Schema;
  Request: PostApiProjectsProjectIdTeamsTeamIdJoinTokenMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdTeamsTeamIdJoinTokenPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdTeamsTeamIdJoinToken401Schema
    | PostApiProjectsProjectIdTeamsTeamIdJoinToken403Schema
    | PostApiProjectsProjectIdTeamsTeamIdJoinToken404Schema
    | PostApiProjectsProjectIdTeamsTeamIdJoinToken422Schema;
};
