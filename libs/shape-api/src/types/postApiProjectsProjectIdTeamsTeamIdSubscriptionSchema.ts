/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { TeamSubscriptionUpdateResultSchema } from './teamSubscriptionUpdateResultSchema';

export type PostApiProjectsProjectIdTeamsTeamIdSubscriptionPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  team_id: string;
};

/**
 * @description Subscription creation
 */
export type PostApiProjectsProjectIdTeamsTeamIdSubscription200Schema = TeamSubscriptionUpdateResultSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdTeamsTeamIdSubscription401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdTeamsTeamIdSubscription403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdTeamsTeamIdSubscription404Schema = unknown;

/**
 * @description Subscription update failed
 */
export type PostApiProjectsProjectIdTeamsTeamIdSubscription422Schema = ErrorSchema;

export type PostApiProjectsProjectIdTeamsTeamIdSubscriptionMutationRequestSchema = {
  /**
   * @type string | undefined
   */
  new_plan_slug?: string;
};

export type PostApiProjectsProjectIdTeamsTeamIdSubscriptionMutationResponseSchema =
  PostApiProjectsProjectIdTeamsTeamIdSubscription200Schema;

export type PostApiProjectsProjectIdTeamsTeamIdSubscriptionSchemaMutation = {
  Response: PostApiProjectsProjectIdTeamsTeamIdSubscription200Schema;
  Request: PostApiProjectsProjectIdTeamsTeamIdSubscriptionMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdTeamsTeamIdSubscriptionPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdTeamsTeamIdSubscription401Schema
    | PostApiProjectsProjectIdTeamsTeamIdSubscription403Schema
    | PostApiProjectsProjectIdTeamsTeamIdSubscription404Schema
    | PostApiProjectsProjectIdTeamsTeamIdSubscription422Schema;
};
