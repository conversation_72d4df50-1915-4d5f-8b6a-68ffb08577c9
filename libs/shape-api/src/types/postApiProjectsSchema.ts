/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ProjectSchema } from './projectSchema';

/**
 * @description Project created
 */
export type PostApiProjects201Schema = ProjectSchema;

/**
 * @description Authentication required
 */
export type PostApiProjects401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjects403Schema = ErrorSchema;

export type PostApiProjectsMutationRequestSchema = {
  /**
   * @type object | undefined
   */
  project?: {
    /**
     * @type string | undefined
     */
    title?: string;
    /**
     * @type string | undefined
     */
    short_name?: string;
    /**
     * @description The signed id given by the direct upload method
     * @type string | undefined
     */
    logo?: string;
    /**
     * @type string | undefined
     */
    timezone?: string;
  };
  /**
   * @type string | undefined, uuid
   */
  org_id?: string;
};

export type PostApiProjectsMutationResponseSchema = PostApiProjects201Schema;

export type PostApiProjectsSchemaMutation = {
  Response: PostApiProjects201Schema;
  Request: PostApiProjectsMutationRequestSchema;
  Errors: PostApiProjects401Schema | PostApiProjects403Schema;
};
