/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { WeeklyWorkPlanCloseSchema } from './weeklyWorkPlanCloseSchema';

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdClosePathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  weekly_work_plan_id: string;
};

/**
 * @description Weekly Work Plan closed
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdClose200Schema = WeeklyWorkPlanCloseSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdClose401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdClose403Schema = unknown;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdClose404Schema = unknown;

/**
 * @description Close failed
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdClose422Schema = ErrorSchema;

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdCloseMutationResponseSchema =
  PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdClose200Schema;

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdCloseSchemaMutation = {
  Response: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdClose200Schema;
  PathParams: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdClosePathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdClose401Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdClose403Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdClose404Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdClose422Schema;
};
