/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { WeeklyWorkPlanActivitySchema } from './weeklyWorkPlanActivitySchema';
import type { WeeklyWorkPlanActivityStatusesSchema } from './weeklyWorkPlanActivityStatusesSchema';
import type { WeeklyWorkPlanActivityVarianceCategoriesSchema } from './weeklyWorkPlanActivityVarianceCategoriesSchema';

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  weekly_work_plan_id: string;
};

/**
 * @description Weekly work plan activity created
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities201Schema = WeeklyWorkPlanActivitySchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities404Schema = unknown;

/**
 * @description Create failed
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities422Schema = ErrorSchema;

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesMutationRequestSchema = {
  /**
   * @type string
   */
  comment?: string | null;
  /**
   * @type number, float
   */
  expected_percentage_completed?: number | null;
  /**
   * @type string, uuid
   */
  shift_activity_id: string;
  status?: WeeklyWorkPlanActivityStatusesSchema | null;
  variance_category?: WeeklyWorkPlanActivityVarianceCategoriesSchema | null;
  /**
   * @type string
   */
  variance_recovery_mitigation_measures?: string | null;
  /**
   * @type string
   */
  variance_remarks?: string | null;
};

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesMutationResponseSchema =
  PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities201Schema;

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesSchemaMutation = {
  Response: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities201Schema;
  Request: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities400Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities401Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities403Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities404Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities422Schema;
};
