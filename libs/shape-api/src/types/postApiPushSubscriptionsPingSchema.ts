/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';

/**
 * @description Ping sent
 */
export type PostApiPushSubscriptionsPing204Schema = unknown;

/**
 * @description Authentication required
 */
export type PostApiPushSubscriptionsPing401Schema = AuthenticationErrorSchema;

export type PostApiPushSubscriptionsPingMutationResponseSchema = PostApiPushSubscriptionsPing204Schema;

export type PostApiPushSubscriptionsPingSchemaMutation = {
  Response: PostApiPushSubscriptionsPing204Schema;
  Errors: PostApiPushSubscriptionsPing401Schema;
};
