/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { QueuedTaskResultFileDownloadSchema } from './queuedTaskResultFileDownloadSchema';
import type { QueuedTaskResultShowcaseProjectIdSchema } from './queuedTaskResultShowcaseProjectIdSchema';
import type { QueuedTaskResultSmartIssueSchema } from './queuedTaskResultSmartIssueSchema';

export type QueuedTaskResultSchema =
  | (QueuedTaskResultSmartIssueSchema & {
      operation: 'ai_smart_issue';
    })
  | (QueuedTaskResultFileDownloadSchema & {
      operation: 'issue_export';
    })
  | (QueuedTaskResultFileDownloadSchema & {
      operation: 'issue_list_export';
    })
  | (QueuedTaskResultFileDownloadSchema & {
      operation: 'potential_change_export';
    })
  | (QueuedTaskResultFileDownloadSchema & {
      operation: 'shift_activity_list_export';
    })
  | (QueuedTaskResultFileDownloadSchema & {
      operation: 'shift_report_export';
    })
  | (QueuedTaskResultFileDownloadSchema & {
      operation: 'shift_report_list_export';
    })
  | (QueuedTaskResultShowcaseProjectIdSchema & {
      operation: 'showcase_project_creation';
    })
  | (QueuedTaskResultFileDownloadSchema & {
      operation: 'weekly_work_plan_export';
    })
  | (QueuedTaskResultFileDownloadSchema & {
      operation: 'weekly_work_plan_lookback_export';
    });
