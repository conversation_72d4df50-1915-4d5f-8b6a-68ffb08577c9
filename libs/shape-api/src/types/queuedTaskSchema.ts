/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { QueuedTaskResultSchema } from './queuedTaskResultSchema';

export const queuedTaskStatusEnum = {
  pending: 'pending',
  expired: 'expired',
  failed: 'failed',
  completed: 'completed',
} as const;

export type QueuedTaskStatusEnumSchema = (typeof queuedTaskStatusEnum)[keyof typeof queuedTaskStatusEnum];

export type QueuedTaskSchema = QueuedTaskResultSchema & {
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type string
   */
  status: QueuedTaskStatusEnumSchema;
};
