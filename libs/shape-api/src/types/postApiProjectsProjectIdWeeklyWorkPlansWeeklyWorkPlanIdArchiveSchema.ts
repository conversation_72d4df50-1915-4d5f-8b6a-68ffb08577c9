/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { WeeklyWorkPlanSchema } from './weeklyWorkPlanSchema';

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdArchivePathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  weekly_work_plan_id: string;
};

/**
 * @description Weekly Work Plan archived
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdArchive200Schema = WeeklyWorkPlanSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdArchive401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdArchive403Schema = unknown;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdArchive404Schema = unknown;

/**
 * @description Archive failed
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdArchive422Schema = ErrorSchema;

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdArchiveMutationResponseSchema =
  PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdArchive200Schema;

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdArchiveSchemaMutation = {
  Response: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdArchive200Schema;
  PathParams: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdArchivePathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdArchive401Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdArchive403Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdArchive404Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdArchive422Schema;
};
