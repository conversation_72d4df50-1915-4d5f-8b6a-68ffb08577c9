/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { ShiftReportResourceAllocationSchema } from './shiftReportResourceAllocationSchema';

export type ShiftReportMaterialSchema = {
  /**
   * @type array
   */
  activities: ShiftReportResourceAllocationSchema[];
  /**
   * @type string
   */
  description: string | null;
  /**
   * @type integer
   */
  documentCount: number;
  /**
   * @type array
   */
  downTimes: ShiftReportResourceAllocationSchema[];
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type string, uuid
   */
  materialResourceId: string | null;
  /**
   * @type number, float
   */
  quantity: number | null;
  /**
   * @type integer
   */
  teamMemberId: number;
  /**
   * @type string
   */
  units: string | null;
};
