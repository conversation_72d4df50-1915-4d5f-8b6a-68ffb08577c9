/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';

export type PostApiProjectsProjectIdTeamsTeamIdResendMembersInvitesPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  team_id: string;
};

/**
 * @description Invite sent
 */
export type PostApiProjectsProjectIdTeamsTeamIdResendMembersInvites202Schema = unknown;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdTeamsTeamIdResendMembersInvites401Schema = AuthenticationErrorSchema;

/**
 * @description Team not found
 */
export type PostApiProjectsProjectIdTeamsTeamIdResendMembersInvites404Schema = unknown;

export type PostApiProjectsProjectIdTeamsTeamIdResendMembersInvitesMutationResponseSchema =
  PostApiProjectsProjectIdTeamsTeamIdResendMembersInvites202Schema;

export type PostApiProjectsProjectIdTeamsTeamIdResendMembersInvitesSchemaMutation = {
  Response: PostApiProjectsProjectIdTeamsTeamIdResendMembersInvites202Schema;
  PathParams: PostApiProjectsProjectIdTeamsTeamIdResendMembersInvitesPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdTeamsTeamIdResendMembersInvites401Schema
    | PostApiProjectsProjectIdTeamsTeamIdResendMembersInvites404Schema;
};
