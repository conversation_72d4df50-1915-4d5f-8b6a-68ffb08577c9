/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { WeeklyWorkPlanSchema } from './weeklyWorkPlanSchema';

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdRestorePathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  weekly_work_plan_id: string;
};

/**
 * @description Weekly Work Plan restored
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdRestore200Schema = WeeklyWorkPlanSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdRestore401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdRestore403Schema = unknown;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdRestore404Schema = unknown;

/**
 * @description Restore failed
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdRestore422Schema = ErrorSchema;

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdRestoreMutationResponseSchema =
  PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdRestore200Schema;

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdRestoreSchemaMutation = {
  Response: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdRestore200Schema;
  PathParams: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdRestorePathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdRestore401Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdRestore403Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdRestore404Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdRestore422Schema;
};
