/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { OrgSchema } from './orgSchema';

export type TeamSchema = {
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type string
   */
  displayName: string | null;
  /**
   * @type boolean
   */
  independent: boolean;
  /**
   * @type string, uuid
   */
  projectId: string;
  org: OrgSchema | null;
  /**
   * @type object
   */
  availableActions: {
    /**
     * @type boolean
     */
    delete: boolean;
    /**
     * @type boolean
     */
    edit: boolean;
    /**
     * @type boolean
     */
    inviteUser: boolean;
    /**
     * @type boolean
     */
    manageJoinToken: boolean;
    /**
     * @type boolean
     */
    manageResource: boolean;
  };
};
