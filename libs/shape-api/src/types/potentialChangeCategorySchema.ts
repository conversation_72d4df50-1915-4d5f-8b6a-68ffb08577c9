/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

export const potentialChangeCategoryEnum = {
  internal: 'internal',
  client_issues: 'client_issues',
  undefined: 'undefined',
} as const;

export type PotentialChangeCategoryEnumSchema =
  (typeof potentialChangeCategoryEnum)[keyof typeof potentialChangeCategoryEnum];

export type PotentialChangeCategorySchema = PotentialChangeCategoryEnumSchema;
