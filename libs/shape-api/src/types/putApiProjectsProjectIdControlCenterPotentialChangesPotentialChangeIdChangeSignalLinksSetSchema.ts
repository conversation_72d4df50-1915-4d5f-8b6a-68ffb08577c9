/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ChangeSignalsBodyParameterSchema } from './changeSignalsBodyParameterSchema';
import type { ErrorSchema } from './errorSchema';
import type { PotentialChangeSchema } from './potentialChangeSchema';

export type PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksSetPathParamsSchema =
  {
    /**
     * @type string, uuid
     */
    project_id: string;
    /**
     * @type string, uuid
     */
    potential_change_id: string;
  };

/**
 * @description Links change signals to potential changes
 */
export type PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksSet200Schema =
  PotentialChangeSchema;

/**
 * @description Authentication required
 */
export type PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksSet401Schema =
  AuthenticationErrorSchema;

/**
 * @description Unable to process request
 */
export type PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksSet422Schema =
  ErrorSchema;

export type PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksSetMutationRequestSchema =
  ChangeSignalsBodyParameterSchema;

export type PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksSetMutationResponseSchema =
  PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksSet200Schema;

export type PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksSetSchemaMutation = {
  Response: PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksSet200Schema;
  Request: PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksSetMutationRequestSchema;
  PathParams: PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksSetPathParamsSchema;
  Errors:
    | PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksSet401Schema
    | PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksSet422Schema;
};
