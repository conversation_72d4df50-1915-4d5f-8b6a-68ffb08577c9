/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

export const potentialChangeStatusEnum = {
  unprocessed: 'unprocessed',
  under_preparation: 'under_preparation',
  under_internal_review: 'under_internal_review',
  submitted: 'submitted',
  awaiting_client_review: 'awaiting_client_review',
  client_clarification_requested: 'client_clarification_requested',
  approved_by_client: 'approved_by_client',
  rejected_by_client: 'rejected_by_client',
  canceled: 'canceled',
  instruction_and_implementation: 'instruction_and_implementation',
} as const;

export type PotentialChangeStatusEnumSchema =
  (typeof potentialChangeStatusEnum)[keyof typeof potentialChangeStatusEnum];

export type PotentialChangeStatusSchema = PotentialChangeStatusEnumSchema;
