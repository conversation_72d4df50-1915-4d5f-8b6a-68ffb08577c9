/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

export const potentialChangeEstimatedCostImpactEnum = {
  none: 'none',
  minor: 'minor',
  moderate: 'moderate',
  significant: 'significant',
  major: 'major',
  extensive: 'extensive',
} as const;

export type PotentialChangeEstimatedCostImpactEnumSchema =
  (typeof potentialChangeEstimatedCostImpactEnum)[keyof typeof potentialChangeEstimatedCostImpactEnum];

export type PotentialChangeEstimatedCostImpactSchema = PotentialChangeEstimatedCostImpactEnumSchema;
