/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DocumentReferenceAndDocumentSchema } from './documentReferenceAndDocumentSchema';

export type ShiftReportCommentSchema = {
  /**
   * @type array
   */
  attachments: DocumentReferenceAndDocumentSchema[];
  /**
   * @type object
   */
  availableActions: {
    /**
     * @type boolean
     */
    delete: boolean;
    /**
     * @type string, date-time
     */
    gracePeriodUntil: string | null;
  };
  /**
   * @type string, date-time
   */
  createdAt: string;
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @description Comment body in plain text
   * @type string
   */
  plainText: string | null;
  /**
   * @description Comment body in rich text
   * @type object
   */
  richText?: object | null;
  /**
   * @type integer
   */
  teamMemberId: number;
};
