/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { WeeklyWorkPlanSchema } from './weeklyWorkPlanSchema';

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicatePathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  weekly_work_plan_id: string;
};

/**
 * @description Weekly Work Plan duplicated
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicate201Schema = WeeklyWorkPlanSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicate400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicate401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicate403Schema = unknown;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicate404Schema = unknown;

/**
 * @description Duplication failed
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicate422Schema = ErrorSchema;

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicateMutationRequestSchema = {
  /**
   * @type string | undefined, date
   */
  end_date?: string;
  /**
   * @type string | undefined, date
   */
  start_date?: string;
  /**
   * @type string | undefined
   */
  title?: string;
};

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicateMutationResponseSchema =
  PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicate201Schema;

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicateSchemaMutation = {
  Response: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicate201Schema;
  Request: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicateMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicatePathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicate400Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicate401Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicate403Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicate404Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicate422Schema;
};
