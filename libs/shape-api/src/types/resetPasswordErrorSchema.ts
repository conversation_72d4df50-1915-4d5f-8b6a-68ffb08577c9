/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

export const resetPasswordErrorErrorCodeEnum = {
  invalid_token: 'invalid_token',
  locked_for_reset_password: 'locked_for_reset_password',
  password_confirmation_not_matching: 'password_confirmation_not_matching',
  reset_password_failed: 'reset_password_failed',
  reset_period_expired: 'reset_period_expired',
} as const;

export type ResetPasswordErrorErrorCodeEnumSchema =
  (typeof resetPasswordErrorErrorCodeEnum)[keyof typeof resetPasswordErrorErrorCodeEnum];

export type ResetPasswordErrorSchema = {
  /**
   * @type string
   */
  errorCode: ResetPasswordErrorErrorCodeEnumSchema;
  /**
   * @type string
   */
  errorDescription: string;
  /**
   * @description Present when errorCode is reset_period_expired
   * @type string
   */
  email: string | null;
};
