/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ResourceSchema } from './resourceSchema';

export type PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdEnablePathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  team_id: string;
  /**
   * @type string, uuid
   */
  resource_id: string;
};

/**
 * @description Resource enabled
 */
export type PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdEnable200Schema = ResourceSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdEnable401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdEnable403Schema = unknown;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdEnable404Schema = unknown;

export type PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdEnableMutationResponseSchema =
  PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdEnable200Schema;

export type PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdEnableSchemaMutation = {
  Response: PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdEnable200Schema;
  PathParams: PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdEnablePathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdEnable401Schema
    | PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdEnable403Schema
    | PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdEnable404Schema;
};
