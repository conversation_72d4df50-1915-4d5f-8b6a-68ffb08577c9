/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { TeamMemberConstructionRoleSchema } from './teamMemberConstructionRoleSchema';
import type { TeamMemberRoleSchema } from './teamMemberRoleSchema';
import type { TeamMemberSchema } from './teamMemberSchema';

export type PostApiProjectsProjectIdTeamsTeamIdMembersPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  team_id: string;
};

/**
 * @description Team member created
 */
export type PostApiProjectsProjectIdTeamsTeamIdMembers201Schema = TeamMemberSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdTeamsTeamIdMembers401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdTeamsTeamIdMembers403Schema = ErrorSchema;

/**
 * @description Project not found
 */
export type PostApiProjectsProjectIdTeamsTeamIdMembers404Schema = unknown;

/**
 * @description Team member not created
 */
export type PostApiProjectsProjectIdTeamsTeamIdMembers422Schema = ErrorSchema;

export type PostApiProjectsProjectIdTeamsTeamIdMembersMutationRequestSchema = {
  /**
   * @type string | undefined
   */
  email?: string;
  /**
   * @type string | undefined
   */
  role?: TeamMemberRoleSchema;
  construction_role?: TeamMemberConstructionRoleSchema | null;
};

export type PostApiProjectsProjectIdTeamsTeamIdMembersMutationResponseSchema =
  PostApiProjectsProjectIdTeamsTeamIdMembers201Schema;

export type PostApiProjectsProjectIdTeamsTeamIdMembersSchemaMutation = {
  Response: PostApiProjectsProjectIdTeamsTeamIdMembers201Schema;
  Request: PostApiProjectsProjectIdTeamsTeamIdMembersMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdTeamsTeamIdMembersPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdTeamsTeamIdMembers401Schema
    | PostApiProjectsProjectIdTeamsTeamIdMembers403Schema
    | PostApiProjectsProjectIdTeamsTeamIdMembers404Schema
    | PostApiProjectsProjectIdTeamsTeamIdMembers422Schema;
};
