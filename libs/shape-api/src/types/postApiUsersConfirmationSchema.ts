/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { ConfirmEmailErrorSchema } from './confirmEmailErrorSchema';
import type { UserSchema } from './userSchema';

/**
 * @description User confirmed
 */
export type PostApiUsersConfirmation200Schema = UserSchema;

/**
 * @description Invalid token
 */
export type PostApiUsersConfirmation400Schema = ConfirmEmailErrorSchema;

/**
 * @description Confirmation failed
 */
export type PostApiUsersConfirmation422Schema = ConfirmEmailErrorSchema;

export type PostApiUsersConfirmationMutationRequestSchema = {
  /**
   * @type string
   */
  confirmation_token: string;
  /**
   * @type string | undefined, uuid
   */
  user_id?: string;
};

export type PostApiUsersConfirmationMutationResponseSchema = PostApiUsersConfirmation200Schema;

export type PostApiUsersConfirmationSchemaMutation = {
  Response: PostApiUsersConfirmation200Schema;
  Request: PostApiUsersConfirmationMutationRequestSchema;
  Errors: PostApiUsersConfirmation400Schema | PostApiUsersConfirmation422Schema;
};
