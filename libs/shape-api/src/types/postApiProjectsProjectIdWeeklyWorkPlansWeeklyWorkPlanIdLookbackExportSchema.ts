/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { QueuedTaskSchema } from './queuedTaskSchema';

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdLookbackExportPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  weekly_work_plan_id: string;
};

/**
 * @description Request accepted
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdLookbackExport202Schema = QueuedTaskSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdLookbackExport401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdLookbackExport403Schema = unknown;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdLookbackExport404Schema = unknown;

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdLookbackExportMutationResponseSchema =
  PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdLookbackExport202Schema;

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdLookbackExportSchemaMutation = {
  Response: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdLookbackExport202Schema;
  PathParams: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdLookbackExportPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdLookbackExport401Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdLookbackExport403Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdLookbackExport404Schema;
};
