/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { PotentialChangeCategorySchema } from './potentialChangeCategorySchema';
import type { PotentialChangeEstimatedCostImpactSchema } from './potentialChangeEstimatedCostImpactSchema';
import type { PotentialChangeEstimatedScheduleImpactSchema } from './potentialChangeEstimatedScheduleImpactSchema';
import type { PotentialChangePrioritySchema } from './potentialChangePrioritySchema';
import type { PotentialChangeStatusSchema } from './potentialChangeStatusSchema';

export type PotentialChangeDetailsBasicSchema = {
  /**
   * @type boolean
   */
  archived: boolean;
  /**
   * @type object
   */
  availableActions: {
    /**
     * @type boolean
     */
    archive: boolean;
    /**
     * @type boolean
     */
    linkChangeSignals: boolean;
    /**
     * @type boolean
     */
    unlinkChangeSignals: boolean;
  };
  category: PotentialChangeCategorySchema | null;
  /**
   * @type string
   */
  comment: string | null;
  /**
   * @type string, date-time
   */
  createdAt: string;
  /**
   * @type boolean
   */
  earlyWarningNoticeSubmitted: boolean | null;
  estimatedCostImpact: PotentialChangeEstimatedCostImpactSchema | null;
  estimatedScheduleImpact: PotentialChangeEstimatedScheduleImpactSchema | null;
  /**
   * @type string, uuid
   */
  id: string;
  priority: PotentialChangePrioritySchema | null;
  /**
   * @type integer
   */
  signalsCount: number;
  /**
   * @type string
   */
  status: PotentialChangeStatusSchema;
  /**
   * @type integer
   */
  teamMemberId: number;
  /**
   * @type string
   */
  title: string | null;
};
