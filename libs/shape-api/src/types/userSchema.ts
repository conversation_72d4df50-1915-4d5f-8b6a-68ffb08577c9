/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

export const userDefaultProductEnum = {
  issues: 'issues',
  shift_manager: 'shift_manager',
  channels: 'channels',
} as const;

export type UserDefaultProductEnumSchema = (typeof userDefaultProductEnum)[keyof typeof userDefaultProductEnum];

export const userOnboardingStateEnum = {
  in_progress: 'in_progress',
  finished: 'finished',
} as const;

export type UserOnboardingStateEnumSchema = (typeof userOnboardingStateEnum)[keyof typeof userOnboardingStateEnum];

export type UserSchema = {
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type string
   */
  avatarUrl: string | null;
  /**
   * @type object
   */
  channels: {
    /**
     * @type string
     */
    streamChatTeam: string;
    /**
     * @type string
     */
    streamChatUserId: string;
  };
  defaultProduct: UserDefaultProductEnumSchema | null;
  /**
   * @type string, uuid
   */
  defaultProject: string | null;
  /**
   * @type string
   */
  email: string;
  /**
   * @type boolean
   */
  emailConfirmed: boolean;
  /**
   * @type string
   */
  firstName: string | null;
  /**
   * @type string
   */
  lastName: string | null;
  /**
   * @type string
   */
  name: string;
  onboardingState: UserOnboardingStateEnumSchema | null;
  /**
   * @type string, date-time
   */
  passwordUpdatedAt: string | null;
  /**
   * @type boolean
   */
  pendingEuaAcceptance: boolean;
  /**
   * @type boolean | undefined
   */
  shapeDemoRepresentative?: boolean;
  /**
   * @type string, date
   */
  signupDate: string | null;
  /**
   * @type string
   */
  timezone: string;
  /**
   * @type string
   */
  unconfirmedEmail: string | null;
};
