/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { IssueCategorySchema } from './issueCategorySchema';
import type { IssueImpactSchema } from './issueImpactSchema';

export type SmartIssueDataSchema = {
  /**
   * @type string | undefined
   */
  category?: IssueCategorySchema;
  /**
   * @type string | undefined
   */
  description?: string;
  /**
   * @type string | undefined, date-time
   */
  dueDate?: string;
  /**
   * @type string | undefined
   */
  impact?: IssueImpactSchema;
  /**
   * @type string | undefined
   */
  subCategory?: string;
  /**
   * @type string | undefined
   */
  title?: string;
};
