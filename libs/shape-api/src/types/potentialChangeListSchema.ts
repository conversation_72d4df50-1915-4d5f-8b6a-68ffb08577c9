/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { CursorPaginationMetaSchema } from './cursorPaginationMetaSchema';
import type { PotentialChangeDetailsBasicSchema } from './potentialChangeDetailsBasicSchema';

export type PotentialChangeListSchema = {
  /**
   * @type array
   */
  entries: PotentialChangeDetailsBasicSchema[];
  /**
   * @type object
   */
  meta: CursorPaginationMetaSchema;
};
