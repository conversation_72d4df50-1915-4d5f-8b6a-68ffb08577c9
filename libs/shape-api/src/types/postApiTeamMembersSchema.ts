/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { CreateTeamMemberWithTokenErrorSchema } from './createTeamMemberWithTokenErrorSchema';
import type { TeamMemberFromJoinTokenSchema } from './teamMemberFromJoinTokenSchema';

/**
 * @description Team member created
 */
export type PostApiTeamMembers201Schema = TeamMemberFromJoinTokenSchema;

/**
 * @description Authentication required
 */
export type PostApiTeamMembers401Schema = AuthenticationErrorSchema;

/**
 * @description Create failed
 */
export type PostApiTeamMembers422Schema = CreateTeamMemberWithTokenErrorSchema;

export type PostApiTeamMembersMutationRequestSchema = {
  /**
   * @description The team join token
   * @type string
   */
  token: string;
};

export type PostApiTeamMembersMutationResponseSchema = PostApiTeamMembers201Schema;

export type PostApiTeamMembersSchemaMutation = {
  Response: PostApiTeamMembers201Schema;
  Request: PostApiTeamMembersMutationRequestSchema;
  Errors: PostApiTeamMembers401Schema | PostApiTeamMembers422Schema;
};
