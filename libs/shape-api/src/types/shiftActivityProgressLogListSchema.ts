/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { CursorPaginationSchema } from './cursorPaginationSchema';
import type { ShiftActivityProgressLogBasicDetailsSchema } from './shiftActivityProgressLogBasicDetailsSchema';
import type { ShiftActivityProgressLogSchema } from './shiftActivityProgressLogSchema';

export type ShiftActivityProgressLogListSchema = CursorPaginationSchema & {
  /**
   * @type array
   */
  entries: (ShiftActivityProgressLogBasicDetailsSchema | ShiftActivityProgressLogSchema)[];
};
