/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { QueuedTaskSchema } from './queuedTaskSchema';

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdExportPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  weekly_work_plan_id: string;
};

/**
 * @description Request accepted
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdExport202Schema = QueuedTaskSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdExport401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdExport403Schema = unknown;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdExport404Schema = unknown;

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdExportMutationResponseSchema =
  PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdExport202Schema;

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdExportSchemaMutation = {
  Response: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdExport202Schema;
  PathParams: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdExportPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdExport401Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdExport403Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdExport404Schema;
};
