/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { ShiftActivityOverviewDailyProgressEntryProgressLogPartialSchema } from './shiftActivityOverviewDailyProgressEntryProgressLogPartialSchema';
import type { ShiftActivityOverviewDailyProgressEntryShiftReportPartialSchema } from './shiftActivityOverviewDailyProgressEntryShiftReportPartialSchema';

export type ShiftActivityOverviewDailyProgressEntryDiscriminatedPartialSchema =
  | (ShiftActivityOverviewDailyProgressEntryProgressLogPartialSchema & {
      entryType: 'progress_log';
    })
  | (ShiftActivityOverviewDailyProgressEntryShiftReportPartialSchema & {
      entryType: 'shift_report';
    });
