/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { TeamSubscriptionPlanQuotaFeatureSchema } from './teamSubscriptionPlanQuotaFeatureSchema';
import type { TeamSubscriptionPlanTimespanFeatureSchema } from './teamSubscriptionPlanTimespanFeatureSchema';
import type { TeamSubscriptionPlanToggleFeatureSchema } from './teamSubscriptionPlanToggleFeatureSchema';

export type TeamSubscriptionPlanSchema = {
  /**
   * @description Name of the active plan, which will be different from the subscribed plan if on a trial.
   * @type string
   */
  activePlanName: string;
  /**
   * @type string
   */
  activePlanSlug: string;
  /**
   * @type object | undefined
   */
  billing?: {
    /**
     * @type integer
     */
    costPerUserInCents: number;
    /**
     * @type string
     */
    currency: string;
  };
  /**
   * @type object
   */
  features: {
    /**
     * @type object | undefined
     */
    customDashboards?: TeamSubscriptionPlanToggleFeatureSchema;
    /**
     * @type object | undefined
     */
    documentReferences?: TeamSubscriptionPlanToggleFeatureSchema;
    /**
     * @type object | undefined
     */
    exportIssues?: TeamSubscriptionPlanToggleFeatureSchema;
    /**
     * @type object | undefined
     */
    exportShiftReportsData?: TeamSubscriptionPlanToggleFeatureSchema;
    /**
     * @type object
     */
    issueHistory: TeamSubscriptionPlanTimespanFeatureSchema;
    /**
     * @type object | undefined
     */
    issuePrivateChat?: TeamSubscriptionPlanToggleFeatureSchema;
    /**
     * @type object | undefined
     */
    printIssue?: TeamSubscriptionPlanToggleFeatureSchema;
    /**
     * @type object | undefined
     */
    proDashboards?: TeamSubscriptionPlanToggleFeatureSchema;
    /**
     * @type object
     */
    projectTimeline: TeamSubscriptionPlanTimespanFeatureSchema;
    /**
     * @type object | undefined
     */
    shiftReportsManagerView?: TeamSubscriptionPlanToggleFeatureSchema;
    /**
     * @type object
     */
    storageBytes: TeamSubscriptionPlanQuotaFeatureSchema;
    /**
     * @type object
     */
    usersPerTeam: TeamSubscriptionPlanQuotaFeatureSchema;
  };
  /**
   * @type boolean
   */
  subscribedPlanBillable: boolean;
  /**
   * @description Name of the subscribed plan which is not necessarily the active one.
   * @type string
   */
  subscribedPlanName: string;
  /**
   * @type string
   */
  subscribedPlanSlug: string;
  /**
   * @type string, date-time
   */
  trialEndAt: string | null;
  /**
   * @type object
   */
  availableActions: {
    /**
     * @type boolean
     */
    accessBillingPortal: boolean;
    /**
     * @deprecated
     * @type boolean
     */
    billingPortal: boolean;
    /**
     * @type boolean
     */
    edit: boolean;
  };
};
