{"name": "@shape-construction/hooks", "version": "0.0.0", "description": "", "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"compile": "npx tsc", "lint": "biome check .", "lint:autofix": "biome check --write .", "format": "biome format --write", "test": "jest", "test:ci": "jest --ci --coverage"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@biomejs/biome": "1.9.3", "@testing-library/dom": "10.4.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.2.0", "@testing-library/user-event": "14.6.1", "@types/css-mediaquery": "^0.1.1", "@types/jest": "29.4.0", "@types/lodash.isequal": "4.5.8", "@types/react": "18.0.28", "@types/react-dom": "18.3.0", "@types/react-highlight-words": "^0.16.4", "jest": "29.4.3", "react": "18.3.1", "react-dom": "18.3.1", "ts-jest": "^29.4.0", "typescript": "5.8.3"}, "dependencies": {"@react-hook/debounce": "4.0.0", "jest-environment-jsdom": "29.7.0", "lodash.isequal": "4.5.0", "react-use": "17.6.0"}}