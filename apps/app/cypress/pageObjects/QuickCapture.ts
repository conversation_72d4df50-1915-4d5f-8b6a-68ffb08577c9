class QuickCapture {
  get titleInput() {
    return cy.findAllByLabelText(/Title/);
  }

  get images() {
    return cy.findAllByRole('img');
  }

  get addMoreUploadFileButton() {
    return cy.findByRole('button', { name: 'Add' });
  }

  get emptyStateUploadFileButton() {
    return cy.findByLabelText('Upload an image', {
      selector: "input[type='file']",
    });
  }

  get uploadFromDeviceButton() {
    return cy.findByLabelText('Upload from device');
  }

  get createAnother() {
    return cy.findAllByLabelText('Create another');
  }

  get createButton() {
    return cy.findByRole('button', { name: 'Create' });
  }

  get cancelButton() {
    return cy.findByRole('button', { name: 'Cancel' });
  }

  get successToast() {
    return cy.findByText('Your unpublished issue has been saved.').should('be.visible');
  }

  get offlineToast() {
    return cy.findByText(
      "Your issue has been saved. We'll try to upload it later on Issues > Unpublished tab once you're online."
    );
  }

  get offlineToastList() {
    return cy.findAllByText(
      "Your issue has been saved. We'll try to upload it later on Issues > Unpublished tab once you're online."
    );
  }

  // Actions
  uploadFromEmptyState(filePath: Cypress.FixtureData) {
    return this.emptyStateUploadFileButton.attachFile(filePath);
  }

  uploadFromDevice(filePath: Cypress.FixtureData) {
    this.addMoreUploadFileButton.click();
    return this.uploadFromDeviceButton.attachFile(filePath);
  }
}

export default QuickCapture;
