import { potentialChangeFactory } from '@shape-construction/api/factories/control-center';

import { render, screen } from 'tests/test-utils';
import { ChangeDetailsEditableSection } from './ChangeDetailsEditableSection';

describe('<ChangeDetailsEditableSection />', () => {
  it('renders all editable fields', async () => {
    const change = potentialChangeFactory({ title: 'title test' });

    render(<ChangeDetailsEditableSection change={change} />);

    expect(await screen.findByText('controlCenter.commercialTracker.fields.priority')).toBeInTheDocument();
    expect(await screen.findByText('controlCenter.commercialTracker.fields.category')).toBeInTheDocument();
    expect(await screen.findByText('controlCenter.commercialTracker.fields.status')).toBeInTheDocument();
    expect(await screen.findByText('controlCenter.commercialTracker.fields.estimatedCostImpact')).toBeInTheDocument();
    expect(
      await screen.findByText('controlCenter.commercialTracker.fields.estimatedScheduleImpact')
    ).toBeInTheDocument();
    expect(
      await screen.findByText('controlCenter.commercialTracker.fields.earlyWarningNoticeSubmitted')
    ).toBeInTheDocument();
    expect(await screen.findByText('controlCenter.commercialTracker.fields.comments')).toBeInTheDocument();
  });
});
