import {
  changeSignalsDowntimesFactory,
  changeSignalsIssuesFactory,
} from '@shape-construction/api/factories/control-center';
import { locationFactory } from '@shape-construction/api/factories/locations';
import { projectFactory } from '@shape-construction/api/factories/projects';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { userFactory } from '@shape-construction/api/factories/users';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { postApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDeleteMockHandler } from '@shape-construction/api/handlers-factories/projects/control-center';
import { getApiProjectsProjectIdLocationsMockHandler } from '@shape-construction/api/handlers-factories/projects/locations';
import { getApiProjectsProjectIdPeopleMockHandler } from '@shape-construction/api/handlers-factories/projects/team-members';
import { server } from 'tests/mock-server';
import { render, screen } from 'tests/test-utils';
import { PotentialChangeSignalItem } from './PotentialChangeSignalItem';

describe('<PotentialChangeSignalItem />', () => {
  const project = projectFactory({ id: 'project-1', timezone: 'Europe/London' });
  const location = locationFactory({ id: 'location-1', name: 'Test Location' });
  const user = userFactory({ id: 'user-1', name: 'Test user' });
  const teamMember = teamMemberFactory({ id: 1, user });
  const signal = changeSignalsIssuesFactory();
  server.use(
    getApiProjectsProjectIdMockHandler(() => project),
    getApiProjectsProjectIdLocationsMockHandler(() => [location]),
    getApiProjectsProjectIdPeopleMockHandler(() => [teamMember])
  );

  describe('when signal is a downtime', () => {
    it('renders all downtime fields', async () => {
      const signal = changeSignalsDowntimesFactory();
      const { user } = render(
        <PotentialChangeSignalItem onUnlinkChangeSignalClick={() => {}} signal={signal} canUnlink />
      );

      await user.click(screen.getAllByRole('button')[0]);

      expect(screen.getByText('controlCenter.commercialTracker.fields.author')).toBeInTheDocument();
      expect(screen.getByText('controlCenter.commercialTracker.fields.createdAt')).toBeInTheDocument();
      expect(screen.getByText('controlCenter.commercialTracker.fields.shiftType')).toBeInTheDocument();
      expect(screen.getByText('controlCenter.commercialTracker.fields.timeLostInHrs')).toBeInTheDocument();
      expect(screen.getByText('controlCenter.commercialTracker.fields.reason')).toBeInTheDocument();
      expect(screen.getByText('controlCenter.commercialTracker.fields.linkedIssue')).toBeInTheDocument();
      expect(screen.getByText('controlCenter.commercialTracker.fields.description')).toBeInTheDocument();
    });
  });

  describe('when signal is an issue', () => {
    it('renders all issue fields', async () => {
      const signal = changeSignalsIssuesFactory();
      const { user } = render(
        <PotentialChangeSignalItem onUnlinkChangeSignalClick={() => {}} signal={signal} canUnlink />
      );

      await user.click(screen.getAllByRole('button')[0]);

      expect(screen.getByText('controlCenter.commercialTracker.fields.author')).toBeInTheDocument();
      expect(screen.getByText('controlCenter.commercialTracker.fields.createdAt')).toBeInTheDocument();
      expect(screen.getByText('controlCenter.commercialTracker.fields.impact')).toBeInTheDocument();
      expect(screen.getByText('controlCenter.commercialTracker.fields.status')).toBeInTheDocument();
      expect(screen.getByText('controlCenter.commercialTracker.fields.description')).toBeInTheDocument();
      expect(screen.getByText('controlCenter.commercialTracker.fields.discipline')).toBeInTheDocument();
      expect(screen.getByText('controlCenter.commercialTracker.fields.location')).toBeInTheDocument();
      expect(screen.getByText('controlCenter.commercialTracker.fields.observedAt')).toBeInTheDocument();
      expect(screen.getByText('controlCenter.commercialTracker.fields.dueDate')).toBeInTheDocument();
      expect(screen.getByText('controlCenter.commercialTracker.fields.plannedClosureDate')).toBeInTheDocument();
      expect(screen.getByText('controlCenter.commercialTracker.fields.closedAt')).toBeInTheDocument();
      expect(screen.getByText('controlCenter.commercialTracker.fields.delayStart')).toBeInTheDocument();
      expect(screen.getByText('controlCenter.commercialTracker.fields.delayFinish')).toBeInTheDocument();
    });
  });

  describe('when canUnlink is true', () => {
    it('renders unlink button', async () => {
      render(<PotentialChangeSignalItem onUnlinkChangeSignalClick={() => {}} signal={signal} canUnlink />);

      expect(
        await screen.findByRole('button', {
          name: 'controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.unlink',
        })
      ).toBeInTheDocument();
    });
  });

  describe('when canUnlink is false', () => {
    it('does not render unlink button', async () => {
      render(<PotentialChangeSignalItem onUnlinkChangeSignalClick={() => {}} signal={signal} canUnlink={false} />);

      const unlinkButton = screen.queryByRole('button', {
        name: 'controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.unlink',
      });
      expect(unlinkButton).not.toBeInTheDocument();
    });
  });

  describe('when the isUnlinkingChangeSignals prop is true', () => {
    it('disables the unlink button', async () => {
      server.use(
        postApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDeleteMockHandler()
      );

      render(
        <PotentialChangeSignalItem
          onUnlinkChangeSignalClick={() => {}}
          signal={signal}
          isUnlinkingChangeSignals={true}
          canUnlink
        />
      );

      expect(
        await screen.findByRole('button', {
          name: 'controlCenter.commercialTracker.modals.potentialChangeLinkSignalsDrawer.unlink',
        })
      ).toBeDisabled();
    });
  });
});
