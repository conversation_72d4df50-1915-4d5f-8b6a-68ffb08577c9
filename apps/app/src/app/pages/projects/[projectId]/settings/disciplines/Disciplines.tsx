import { useMessageGetter } from '@messageformat/react';
import type { DisciplineSchema } from '@shape-construction/api/src/types';
import {
  IconButton,
  Page,
  Tree,
  TreeItem,
  TreeItemActions,
  TreeItemContent,
  TreeItemMenu,
} from '@shape-construction/arch-ui';
import {
  ArrowDownIcon,
  ArrowUpIcon,
  EllipsisVerticalIcon,
  PencilIcon,
  PlusIcon,
  Square2StackIcon,
  TrashIcon,
} from '@shape-construction/arch-ui/src/Icons/solid';
import type { NodeModel, TreeMethods, TreeProps } from '@shape-construction/arch-ui/src/Tree/Tree';
import { useContentEditable } from '@shape-construction/hooks';
import { useSuspenseQuery } from '@tanstack/react-query';
import {
  DISCIPLINE_SHORTCODE_LIMIT,
  disciplineLevel0,
  isFirstDisciplineChild,
  isLastDisciplineChild,
} from 'app/components/Utils/disciplines';
import { getProjectQueryOptions } from 'app/queries/projects/projects';
import { type APIError, removeFirst } from 'app/store/errors/errorSlice';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import { CannotDeleteProjectDisciplineModal } from './components/CannotDeleteProjectDisciplineModal';
import { ProjectDisciplineToolbar } from './components/ProjectDisciplineToolbar';
import {
  type ProjectDisciplineFormValues,
  ProjectDisciplinesAddEditModal,
} from './components/ProjectDisciplinesAddEditModal';
import { ProjectDisciplinesDeleteConfirmationModal } from './components/ProjectDisciplinesDeleteConfirmationModal';
import { ResponseErrorDeleteDisciplineModal } from './components/ResponseErrorDeleteDisciplineModal';
import { useProjectDisciplinesTree } from './hooks/useProjectDisciplinesTree';
import { useProjectDisciplinesTreeState } from './hooks/useProjectDisciplinesTreeState';

type Params = { projectId: string };

const defaultValues: ProjectDisciplineFormValues = {
  name: '',
  shortCode: '',
};

export const Disciplines = () => {
  const dispatch = useDispatch();
  const disciplinesMessages = useMessageGetter('admin.disciplines');
  const { projectId } = useParams() as Params;
  const treeRef = useRef<TreeMethods>(null);
  const navigate = useNavigate();

  const [parentNodeId, setParentNodeId] = useState<string>();
  const [discipline, setDiscipline] = useState<NodeModel<DisciplineSchema>['data']>();
  const [deleteError, setDeleteError] = useState<APIError | null>(null);
  const [disciplineToDelete, setDisciplineToDelete] = useState<NodeModel<DisciplineSchema> | null>(null);

  const getEditableProps = useContentEditable();
  const isUpdate = !!discipline;
  const isCreate = !!parentNodeId;
  const isModalOpen = isUpdate || isCreate;

  const {
    disciplinesTree,
    isLoading,
    createProjectDiscipline,
    updateProjectDiscipline,
    deleteProjectDiscipline,
    duplicateProjectDiscipline,
    moveUpDiscipline,
    moveDownDiscipline,
  } = useProjectDisciplinesTree(projectId);
  const { setCheckedDisciplines, openDiscipline } = useProjectDisciplinesTreeState(treeRef);
  const { data: project } = useSuspenseQuery(getProjectQueryOptions(projectId));
  const hasPermissionToManageDisciplines = project.availableActions.manageDisciplines;

  const hasChildren = (disciplines: NodeModel<DisciplineSchema>[], dis: NodeModel<DisciplineSchema> | null) => {
    return !!disciplines.find(({ parent }) => parent === dis?.id);
  };

  const withChildren = hasChildren(disciplinesTree, disciplineToDelete);

  const openAddEditModal = (parentId: string) => {
    setParentNodeId(parentId);
  };

  const closeAddEditModal = () => {
    setParentNodeId(undefined);
    setDiscipline(undefined);
  };

  const closeDeleteConfirmationModal = () => {
    setDisciplineToDelete(null);
  };

  const onCloseErrorDeleteDisciplineModal = () => {
    setDisciplineToDelete(null);
    setDeleteError(null);
  };

  const onDrop: TreeProps<DisciplineSchema>['onDrop'] = (_tree, { dragSourceId, dropTargetId }) => {
    updateProjectDiscipline({
      projectId,
      disciplineId: dragSourceId as string,
      data: { parent_discipline_id: String(dropTargetId) },
    });
    openDiscipline([parentNodeId as string]);
  };

  const onEdit = (values: NodeModel<DisciplineSchema>) => {
    setDiscipline(values.data);
  };

  const onDuplicate = (node: NodeModel<DisciplineSchema>) => {
    duplicateProjectDiscipline(disciplinesTree, node);
  };

  const onMoveUp = (disciplineId: DisciplineSchema['id']) => {
    moveUpDiscipline(disciplineId);
  };

  const onMoveDown = (disciplineId: DisciplineSchema['id']) => {
    moveDownDiscipline(disciplineId);
  };

  const onSubmitCreateOrEdit = async ({ name, shortCode }: ProjectDisciplineFormValues) => {
    if (isUpdate) {
      await updateProjectDiscipline({
        projectId,
        disciplineId: discipline.id,
        data: {
          name,
          short_code: shortCode,
        },
      });
    }

    if (isCreate) {
      createProjectDiscipline({
        projectId,
        data: {
          name,
          short_code: shortCode,
          parent_discipline_id: parentNodeId,
        },
      });
    }

    openDiscipline([parentNodeId as string]);
    closeAddEditModal();
  };

  const updateDisciplineName = (name: ProjectDisciplineFormValues['name'], disciplineId: DisciplineSchema['id']) => {
    updateProjectDiscipline({
      projectId,
      disciplineId,
      data: {
        name,
      },
    });
  };

  const updateDisciplineShortCode = (
    shortCode: ProjectDisciplineFormValues['name'],
    disciplineId: DisciplineSchema['id']
  ) => {
    updateProjectDiscipline({
      projectId,
      disciplineId,
      data: {
        short_code: shortCode,
      },
    });
  };

  const onDelete = async () => {
    if (!withChildren) {
      try {
        await deleteProjectDiscipline({
          projectId,
          disciplineId: disciplineToDelete?.id as DisciplineSchema['id'],
        });
      } catch (e) {
        setDeleteError(e as APIError);
        // Since we are handling this error locally, we need to immediately
        // remove it from the Redux store to avoid displaying the global network error modal.
        dispatch(removeFirst());
      } finally {
        closeDeleteConfirmationModal();
      }
    }
  };

  useEffect(() => {
    if (!hasPermissionToManageDisciplines) {
      navigate(`/projects/${projectId}`, { replace: true });
    }
  }, [hasPermissionToManageDisciplines, navigate, projectId]);

  if (!hasPermissionToManageDisciplines || isLoading) return null;

  return (
    <Page data-cy="project-disciplines">
      <Page.Header title={disciplinesMessages('title')} />
      <ProjectDisciplineToolbar treeRef={treeRef} projectId={projectId} />
      <Page.Body className="px-4 py-6 md:px-8 md:py-6 bg-white overflow-x-auto">
        <div className="max-w-5xl">
          <Tree
            ref={treeRef}
            rootId="root"
            data={disciplinesTree}
            initialOpen
            onDrop={onDrop}
            onCheck={setCheckedDisciplines}
            canDrag={() => true}
            render={(node, params) => {
              return (
                <TreeItem aria-label={node.data?.name} node={node} params={params}>
                  <TreeItemContent>
                    <span
                      className="hidden sm:block truncate"
                      title={node.data?.name}
                      {...(node.id === disciplineLevel0.id
                        ? {}
                        : getEditableProps({
                            'aria-label': 'discipline-name',
                            onSubmit: (value) => updateDisciplineName(value as string, node.data!.id),
                          }))}
                    >
                      {node.data?.name}
                    </span>
                    <span
                      className="sm:text-gray-400"
                      {...(node.id === disciplineLevel0.id
                        ? {}
                        : getEditableProps({
                            'aria-label': 'discipline-shortCode',
                            maxLength: DISCIPLINE_SHORTCODE_LIMIT,
                            onSubmit: (value) => updateDisciplineShortCode(value as string, node.data!.id),
                          }))}
                    >
                      {node.data?.shortCode}
                    </span>
                  </TreeItemContent>
                  <TreeItemActions>
                    <TreeItemMenu
                      aria-label="Discipline options"
                      icon={EllipsisVerticalIcon}
                      options={[
                        {
                          label: disciplinesMessages('actions.moveUp'),
                          icon: ArrowUpIcon,
                          onClick: () => onMoveUp(node.data?.id as string),
                          disabled: isFirstDisciplineChild(node) || node.data?.id === disciplineLevel0.id,
                        },
                        {
                          label: disciplinesMessages('actions.moveDown'),
                          icon: ArrowDownIcon,
                          onClick: () => onMoveDown(node.data?.id as string),
                          disabled: isLastDisciplineChild(node) || node.data?.id === disciplineLevel0.id,
                        },
                        {
                          label: disciplinesMessages('actions.edit'),
                          icon: PencilIcon,
                          onClick: () => onEdit(node),
                          disabled: node.data?.id === disciplineLevel0.id,
                        },
                        {
                          label: disciplinesMessages('actions.duplicate'),
                          icon: Square2StackIcon,
                          onClick: () => onDuplicate(node),
                        },
                        {
                          label: disciplinesMessages('actions.delete'),
                          icon: TrashIcon,
                          onClick: () => setDisciplineToDelete(node),
                          disabled: node.data?.id === disciplineLevel0.id,
                        },
                      ]}
                    />
                    <IconButton
                      color="secondary"
                      icon={PlusIcon}
                      onClick={() => openAddEditModal(node.id.toString())}
                      size="xs"
                      variant="text"
                      aria-label="add children"
                    />
                  </TreeItemActions>
                </TreeItem>
              );
            }}
          />
          <ProjectDisciplinesAddEditModal
            mode={isCreate ? 'add' : 'edit'}
            isOpen={isModalOpen}
            onClose={closeAddEditModal}
            onSubmit={onSubmitCreateOrEdit}
            initialValues={{
              name: discipline?.name || defaultValues.name,
              shortCode: discipline?.shortCode || defaultValues.shortCode,
            }}
          />

          <ProjectDisciplinesDeleteConfirmationModal
            isOpen={!!disciplineToDelete && !withChildren}
            onCancel={closeDeleteConfirmationModal}
            onConfirm={onDelete}
          />

          <CannotDeleteProjectDisciplineModal
            isOpen={!!disciplineToDelete && withChildren}
            messageNode="admin.disciplines.delete.disciplineHasChildren"
            onClose={closeDeleteConfirmationModal}
          />

          <ResponseErrorDeleteDisciplineModal error={deleteError} onClose={onCloseErrorDeleteDisciplineModal} />
        </div>
      </Page.Body>
    </Page>
  );
};

export { Disciplines as Component };
