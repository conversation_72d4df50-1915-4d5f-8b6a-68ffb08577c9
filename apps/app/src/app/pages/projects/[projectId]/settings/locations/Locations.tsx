import { useMessageGetter } from '@messageformat/react';
import type { LocationSchema } from '@shape-construction/api/src/types';
import type { TreeProps } from '@shape-construction/arch-ui';
import {
  IconButton,
  Page,
  Tree,
  TreeItem,
  TreeItemActions,
  TreeItemContent,
  TreeItemMenu,
} from '@shape-construction/arch-ui';
import {
  ArrowDownIcon,
  ArrowUpIcon,
  EllipsisVerticalIcon,
  PencilIcon,
  PlusIcon,
  Square2StackIcon,
  TrashIcon,
} from '@shape-construction/arch-ui/src/Icons/solid';
import type { NodeModel, TreeMethods } from '@shape-construction/arch-ui/src/Tree/Tree';
import { useContentEditable } from '@shape-construction/hooks';
import { useSuspenseQuery } from '@tanstack/react-query';
import { LOCATION_SHORTCODE_LIMIT, isFirstLocationChild, isLastLocationChild } from 'app/components/Utils/locations';
import { getProjectQueryOptions } from 'app/queries/projects/projects';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import { type APIError, removeFirst } from '../../../../../store/errors/errorSlice';
import { CannotDeleteProjectLocationModal } from './components/CannotDeleteProjectLocationModal';
import { ProjectLocationToolbar } from './components/ProjectLocationToolbar';
import {
  type ProjectLocationFormValues,
  ProjectLocationsAddEditModal,
} from './components/ProjectLocationsAddEditModal';
import { ProjectLocationsDeleteConfirmationModal } from './components/ProjectLocationsDeleteConfirmationModal';
import { ResponseErrorDeleteLocationModal } from './components/ResponseErrorDeleteLocationModal';
import { useProjectLocationsTree } from './hooks/useProjectLocationsTree';
import { useProjectLocationsTreeState } from './hooks/useProjectLocationsTreeState';

type Params = { projectId: string };

const defaultValues: ProjectLocationFormValues = {
  name: '',
  shortCode: '',
};

export const Locations = () => {
  const dispatch = useDispatch();
  const locationsMessages = useMessageGetter('admin.locations');
  const { projectId } = useParams() as Params;
  const navigate = useNavigate();
  const [parentNodeId, setParentNodeId] = useState<string>();
  const treeRef = useRef<TreeMethods>(null);
  const [location, setLocation] = useState<NodeModel<LocationSchema>['data']>();
  const [deleteError, setDeleteError] = useState<APIError | null>(null);
  const [locationToDelete, setLocationToDelete] = useState<NodeModel<LocationSchema> | null>(null);
  const getEditableProps = useContentEditable();
  const isUpdate = !!location;
  const isCreate = !!parentNodeId;
  const isModalOpen = isUpdate || isCreate;
  const {
    locationsTree,
    isLoading,
    createProjectLocation,
    duplicateProjectLocation,
    deleteProjectLocation,
    updateProjectLocation,
    moveUpLocation,
    moveDownLocation,
  } = useProjectLocationsTree(projectId!);
  const { setCheckedLocations, openLocation } = useProjectLocationsTreeState(treeRef);
  const { data: project } = useSuspenseQuery(getProjectQueryOptions(projectId));
  const hasPermissionToManageLocations = project.availableActions.manageLocations;

  useEffect(() => {
    if (!hasPermissionToManageLocations) {
      navigate(`/projects/${projectId}`, { replace: true });
    }
  }, [hasPermissionToManageLocations, navigate, projectId]);

  const openAddEditModal = (parentId: string) => {
    setParentNodeId(parentId);
  };

  const hasChildren = (locations: NodeModel<LocationSchema>[], loc: NodeModel<LocationSchema> | null) => {
    return !!locations.find(({ parent }) => parent === loc?.id);
  };

  const withChildren = hasChildren(locationsTree, locationToDelete);

  const closeAddEditModal = () => {
    setParentNodeId(undefined);
    setLocation(undefined);
  };

  const closeDeleteConfirmationModal = () => {
    setLocationToDelete(null);
  };

  const onDrop: TreeProps<LocationSchema>['onDrop'] = (_tree, { dragSourceId, dropTargetId }) => {
    updateProjectLocation({
      projectId,
      locationId: dragSourceId as string,
      data: { parent_location_id: dropTargetId as string },
    });
    openLocation([parentNodeId as string]);
  };

  const onEdit = (values: NodeModel<LocationSchema>) => {
    setLocation(values.data);
  };

  const onDelete = async () => {
    if (!withChildren) {
      try {
        await deleteProjectLocation({
          projectId,
          locationId: locationToDelete?.id as LocationSchema['id'],
        });
      } catch (e) {
        setDeleteError(e as APIError);
        // Since we are handling this error locally, we need to immediately
        // remove it from the Redux store to avoid displaying the global network error modal.
        dispatch(removeFirst());
      } finally {
        closeDeleteConfirmationModal();
      }
    }
  };

  const onCloseErrorDeleteLocationModal = () => {
    setLocationToDelete(null);
    setDeleteError(null);
  };

  const onDuplicate = (node: NodeModel<LocationSchema>) => {
    duplicateProjectLocation(locationsTree, node);
  };

  const onMoveUp = (locationId: LocationSchema['id']) => {
    moveUpLocation(locationId);
  };

  const onMoveDown = (locationId: LocationSchema['id']) => {
    moveDownLocation(locationId);
  };

  const onSubmit = async ({ name, shortCode }: ProjectLocationFormValues) => {
    if (isUpdate) {
      await updateProjectLocation({
        projectId,
        locationId: location.id,
        data: {
          name,
          short_code: shortCode,
        },
      });
    }

    if (isCreate) {
      createProjectLocation({
        projectId,
        data: {
          name,
          short_code: shortCode,
          parent_location_id: parentNodeId,
        },
      });
    }
    openLocation([parentNodeId as string]);
    closeAddEditModal();
  };

  const updateLocationName = (name: ProjectLocationFormValues['name'], locationId: LocationSchema['id']) => {
    updateProjectLocation({
      projectId,
      locationId,
      data: {
        name,
      },
    });
  };

  const updateLocationShortCode = (shortCode: ProjectLocationFormValues['name'], locationId: LocationSchema['id']) => {
    updateProjectLocation({
      projectId,
      locationId,
      data: {
        short_code: shortCode,
      },
    });
  };

  if (!hasPermissionToManageLocations || isLoading) return null;

  return (
    <Page data-cy="project-locations">
      <Page.Header title={locationsMessages('title')} />
      <ProjectLocationToolbar treeRef={treeRef} projectId={projectId} />
      <Page.Body className="px-4 py-6 md:px-8 md:py-6 bg-white overflow-x-auto">
        <div className="max-w-5xl">
          <Tree
            ref={treeRef}
            rootId="root"
            data={locationsTree}
            initialOpen
            onDrop={onDrop}
            onCheck={setCheckedLocations}
            canDrag={() => true}
            render={(node, params) => (
              <TreeItem aria-label={node.data?.name} node={node} params={params}>
                <TreeItemContent>
                  <span
                    className="hidden sm:block truncate"
                    title={node.data?.name}
                    {...getEditableProps({
                      'aria-label': 'location-name',
                      onSubmit: (value) => updateLocationName(value as string, node.data!.id),
                    })}
                  >
                    {node.data?.name}
                  </span>
                  <span
                    className="sm:text-gray-400"
                    {...getEditableProps({
                      'aria-label': 'location-shortCode',
                      maxLength: LOCATION_SHORTCODE_LIMIT,
                      onSubmit: (value) => updateLocationShortCode(value as string, node.data!.id),
                    })}
                  >
                    {node.data?.shortCode}
                  </span>
                </TreeItemContent>
                <TreeItemActions>
                  <TreeItemMenu
                    aria-label="Location options"
                    icon={EllipsisVerticalIcon}
                    options={[
                      {
                        label: locationsMessages('actions.moveUp'),
                        icon: ArrowUpIcon,
                        onClick: () => onMoveUp(node.data?.id as string),
                        disabled: isFirstLocationChild(node) || !node.data?.parentLocationId,
                      },
                      {
                        label: locationsMessages('actions.moveDown'),
                        icon: ArrowDownIcon,
                        onClick: () => onMoveDown(node.data?.id as string),
                        disabled: isLastLocationChild(node) || !node.data?.parentLocationId,
                      },
                      {
                        label: locationsMessages('actions.edit'),
                        icon: PencilIcon,
                        onClick: () => onEdit(node),
                        disabled: false,
                      },
                      {
                        label: locationsMessages('actions.duplicate'),
                        icon: Square2StackIcon,
                        onClick: () => onDuplicate(node),
                      },
                      {
                        label: locationsMessages('actions.delete'),
                        icon: TrashIcon,
                        onClick: () => setLocationToDelete(node),
                        disabled: !node.data?.parentLocationId,
                      },
                    ]}
                  />
                  <IconButton
                    color="secondary"
                    icon={PlusIcon}
                    onClick={() => openAddEditModal(node.id.toString())}
                    size="xs"
                    variant="text"
                    aria-label="add children"
                  />
                </TreeItemActions>
              </TreeItem>
            )}
          />
          <ProjectLocationsAddEditModal
            mode={isCreate ? 'add' : 'edit'}
            isOpen={isModalOpen}
            onClose={closeAddEditModal}
            onSubmit={onSubmit}
            initialValues={{
              name: location?.name || defaultValues.name,
              shortCode: location?.shortCode || defaultValues.shortCode,
            }}
          />

          <ProjectLocationsDeleteConfirmationModal
            isOpen={!!locationToDelete && !withChildren}
            onCancel={closeDeleteConfirmationModal}
            onConfirm={onDelete}
          />

          <CannotDeleteProjectLocationModal
            isOpen={!!locationToDelete && withChildren}
            messageNode="admin.locations.delete.locationHasChildren"
            onClose={closeDeleteConfirmationModal}
          />

          <ResponseErrorDeleteLocationModal error={deleteError} onClose={onCloseErrorDeleteLocationModal} />
        </div>
      </Page.Body>
    </Page>
  );
};

export { Locations as Component };
