import { useMessageGetter } from '@messageformat/react';
import { cn } from '@shape-construction/arch-ui';
import { parseDateWithFormat } from '@shape-construction/utils/DateTime';
import React from 'react';
import type { HeatmapColIndex, HeatmapRow } from '../../types';

export interface HeatmapAxisLabelsProps {
  dataRow: HeatmapRow;
  hoveredCol: HeatmapColIndex;
}

export const HeatmapAxisLabels = ({ dataRow, hoveredCol }: HeatmapAxisLabelsProps) => {
  const messages = useMessageGetter('dataBook.page.heatmapDashboard.heatmap.axisLabels');

  return (
    <>
      {dataRow.series.map((cell, index) => {
        const startDateFormatted = parseDateWithFormat(cell.startDate, 'DD-MMM');
        const endDateFormatted = parseDateWithFormat(cell.endDate, 'DD-MMM YYYY');
        return (
          <th
            key={startDateFormatted}
            className={cn(
              'text-center font-normal text-[10px] leading-[14px] text-neutral-subtle',
              'w-full opacity-100 p-0'
            )}
          >
            <div className="w-full h-full py-1 px-2 flex">
              <div
                className={cn('m-auto', {
                  'opacity-40': hoveredCol !== null && hoveredCol !== index,
                  'font-semibold text-neutral': hoveredCol !== null,
                })}
              >
                <div className="whitespace-nowrap">
                  {startDateFormatted} {messages('to')}
                </div>
                <div className="w-11/12">{endDateFormatted}</div>
              </div>
            </div>
          </th>
        );
      })}
    </>
  );
};
