import type { DataHealthDashboardScoreSchema } from '@shape-construction/api/src/types';
import { cn } from '@shape-construction/arch-ui';
import React from 'react';
import type { HeatmapLevel } from '../../types';
import { HEATMAP_LEVELS, HEATMAP_THEME, getHeatmapColorClasses } from './heatmap-config';

export interface HeatmapCellCountLabelProps {
  dataPoint: DataHealthDashboardScoreSchema;
  level: HeatmapLevel;
}

export const HeatmapCellCountLabel = ({ dataPoint, level }: HeatmapCellCountLabelProps) => {
  const { recordCount } = dataPoint;
  if (recordCount == null || Number.isNaN(recordCount) || recordCount < 0) return null;
  if (!HEATMAP_LEVELS.includes(level)) return null;

  const labelClasses = getHeatmapColorClasses(HEATMAP_THEME, level, 'label');
  return <div className={cn('text-sm leading-4 font-medium', labelClasses)}>{Math.floor(recordCount)}</div>;
};
