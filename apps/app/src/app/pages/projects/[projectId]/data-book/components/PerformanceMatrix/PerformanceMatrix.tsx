import type { DataHealthDashboardScoreSchema } from '@shape-construction/api/src/types';
import { cn } from '@shape-construction/arch-ui';
import { useStableCallback } from '@shape-construction/hooks';
import React, { useState } from 'react';
import type { HeatmapColIndex, HeatmapGrid } from '../../types';
import { HeatmapAxisLabels } from './HeatmapAxisLabels';
import { HeatmapEmptyState } from './HeatmapEmptyState';
import { HeatmapLegend } from './HeatmapLegend';
import { HeatmapRowScores } from './HeatmapRowScores';
import { HeatmapRowTeamMember } from './HeatmapRowTeamMember';

export interface PerformanceMatrixProps {
  data: HeatmapGrid;
  onCellClick: (dataPoint: DataHealthDashboardScoreSchema) => void;
}

export const PerformanceMatrix = ({ data, onCellClick }: PerformanceMatrixProps) => {
  const [hoveredCol, setHoveredCol] = useState<HeatmapColIndex>(null);

  const handleCellHover = useStableCallback((colIndex: number | null) => {
    setHoveredCol(colIndex);
  });

  if (data.length === 0) {
    return <HeatmapEmptyState />;
  }

  return (
    <div className="flex flex-col">
      <HeatmapLegend />

      <div className="h-full pb-4">
        <table className="group/matrix table-fixed border-collapse w-full">
          <colgroup>
            <col className="w-52 max-[460px]:w-32" />
            <col span={data[0].series.length} className="min-w-16" />
          </colgroup>

          <thead className="h-[50px] sticky top-0 z-10 bg-white shadow-sm">
            <tr>
              <th scope="row" />
              <HeatmapAxisLabels dataRow={data[0]} hoveredCol={hoveredCol} />
            </tr>
          </thead>

          <tbody>
            {data.map((dataRow) => (
              <tr
                key={dataRow.teamMemberId}
                className={cn(
                  'border-y border-neutral-subtlest group/row',
                  'hover:outline-solid hover:outline-1 hover:outline-gray-500',
                  'hover:first:border-t hover:first:border-gray-500'
                )}
              >
                <HeatmapRowTeamMember dataRow={dataRow} />
                <HeatmapRowScores dataRow={dataRow} onCellClick={onCellClick} onCellHover={handleCellHover} />
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};
