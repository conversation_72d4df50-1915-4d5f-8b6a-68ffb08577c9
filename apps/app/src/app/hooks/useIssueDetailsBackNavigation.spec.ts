import { createMemoryHistory } from 'history';
import { act, renderHook } from 'tests/test-utils';
import { useIssueDetailsBackNavigation } from './useIssueDetailsBackNavigation';

describe('useIssueDetailsBackNavigation', () => {
  describe('.goBack', () => {
    it('navigates to the issues', async () => {
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/issues/issue-0'],
      });
      const route = { path: '/projects/:projectId/issues/:issueId' };
      const { result } = renderHook(() => useIssueDetailsBackNavigation(), {
        history,
        route,
      });

      act(() => {
        result.current.goBack();
      });

      expect(history.location.pathname).toBe('/projects/project-0/issues');
    });

    describe('when have query string', () => {
      it('navigates to the url without the query string', async () => {
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/issues?issueId=issue-0'],
        });
        const route = { path: '/projects/:projectId/issues' };
        const { result } = renderHook(() => useIssueDetailsBackNavigation(), {
          history,
          route,
        });

        act(() => {
          result.current.goBack();
        });

        expect(history.location.pathname).toBe('/projects/project-0/issues');
      });
    });

    describe('when has a backTo in state', () => {
      it('navigates to the backTo url', () => {
        const history = createMemoryHistory({ initialEntries: ['/home'] });
        const route = { path: '/projects/:projectId/issues' };
        history.push('/projects/project-0/issues', { backTo: { pathname: '/home' } });

        const { result } = renderHook(() => useIssueDetailsBackNavigation(), {
          history,
          route,
        });

        act(() => {
          result.current.goBack();
        });

        expect(history.location.pathname).toBe('/home');
      });
    });
  });

  describe('.goBackRouteMessage', () => {
    it('returns the label with parent path', () => {
      const history = createMemoryHistory({
        initialEntries: ['/projects/:projectId/issues', '/projects/:projectId/issues/issue-0'],
        initialIndex: 1,
      });
      const route = { path: '/projects/:projectId/issues/:issueId' };

      const { result } = renderHook(() => useIssueDetailsBackNavigation(), {
        history,
        route,
      });

      expect(result.current.goBackRouteMessage).toEqual('navigation.issues');
    });

    it('throw error when route is not defined', () => {
      global.console.error = jest.fn();
      const history = createMemoryHistory({
        initialEntries: ['/projects/:projectId/not-exist'],
      });
      const route = { path: '/projects/:projectId/not-exist' };

      expect(() => {
        renderHook(() => useIssueDetailsBackNavigation(), {
          history,
          route,
        });
      }).toThrow(Error('Path not supported'));
    });

    describe('when it has a query string', () => {
      it('returns the label for the last path', () => {
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/timeline?issueId=issue-0'],
        });
        const route = { path: '/projects/:projectId/timeline' };

        const { result } = renderHook(() => useIssueDetailsBackNavigation(), {
          history,
          route,
        });

        expect(result.current.goBackRouteMessage).toEqual('navigation.timeline');
      });

      describe('when scoped for notifications', () => {
        it('returns the label for notifications', () => {
          const history = createMemoryHistory({
            initialEntries: ['/notifications/projects/project-0/timeline?issueId=issue-0'],
          });
          const route = { path: '/notifications/projects/:projectId/timeline' };

          const { result } = renderHook(() => useIssueDetailsBackNavigation(), {
            history,
            route,
          });

          expect(result.current.goBackRouteMessage).toEqual('navigation.notifications');
        });
      });
    });
  });
});
