import type { LocationDescriptor } from 'history';
import { useCallback, useMemo } from 'react';
import { useLocation, useMatch, useNavigate } from 'react-router';

export type LocationBackToState = {
  backTo: LocationDescriptor;
};

const allowBackPaths = ['issues', 'timeline', 'search', 'notifications', 'filter'];

export const useIssueDetailsBackNavigation = () => {
  const navigate = useNavigate();
  const { search, pathname, state } = useLocation();
  const locationState = state as LocationBackToState;

  const goBack = useCallback(() => {
    if (search) return navigate({ pathname, search: '' }, { replace: true });
    if (locationState?.backTo) return navigate(locationState.backTo, { replace: true });

    return navigate({ pathname: pathname.substring(0, pathname.lastIndexOf('/')) }, { replace: true });
  }, [search, navigate, pathname, locationState?.backTo]);

  const notificationRoute = useMatch({ path: '/notifications', end: false })?.pathname;
  const projectRoute = useMatch({ path: '/projects/:projectId/*' })?.pathname;
  const localPathname = notificationRoute || projectRoute;

  const goBackRouteMessage = useMemo(() => {
    if (!localPathname) return '';

    const backPath = allowBackPaths.find((path) => localPathname.includes(path));
    if (!backPath) throw new Error('Path not supported');

    return `navigation.${backPath}`;
  }, [locationState?.backTo, localPathname]);

  return { goBack, goBackRouteMessage };
};
