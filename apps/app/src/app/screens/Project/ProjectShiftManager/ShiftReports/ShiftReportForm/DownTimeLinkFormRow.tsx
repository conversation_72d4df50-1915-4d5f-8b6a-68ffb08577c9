import { useMessageGetter } from '@messageformat/react';
import type { PatchApiProjectsProjectIdShiftReportsShiftReportIdMutationRequestSchema } from '@shape-construction/api/src/types';
import { InputNumber } from '@shape-construction/arch-ui';
import { XCircleIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { InputSelect, type InputSelectOptionProps } from '@shape-construction/arch-ui/src/InputSelect/InputSelect';
import React from 'react';
import type { ShiftReportRowPathname } from './ShiftReportForm';
import { FormRowWithActions } from './components/FormRowWithActions';
import { getArrayPathFromArrayItemPath } from './formUtils';
import { useShiftReportRow } from './useShiftReportRow';
import { useShiftReportRows } from './useShiftReportRows';

type ContractForcesItemDownTimesItem = NonNullable<
  NonNullable<
    PatchApiProjectsProjectIdShiftReportsShiftReportIdMutationRequestSchema['contract_forces']
  >[number]['down_times']
>[number];
type EquipmentsItemDownTimesItem = NonNullable<
  NonNullable<
    PatchApiProjectsProjectIdShiftReportsShiftReportIdMutationRequestSchema['equipments']
  >[number]['down_times']
>[number];
type MaterialsItemDownTimesItem = NonNullable<
  NonNullable<
    PatchApiProjectsProjectIdShiftReportsShiftReportIdMutationRequestSchema['materials']
  >[number]['down_times']
>[number];

export const useDownTimesAsSelectOptions = (
  path: DownTimeLinkFormRowProps['path'],
  currentDownTimeLink: ContractForcesItemDownTimesItem
): InputSelectOptionProps[] => {
  const { fieldRows: downTimes } = useShiftReportRows('down_times');
  const downTimeLinksPath = getArrayPathFromArrayItemPath(path) as
    | `contract_forces.${number}.down_times`
    | `equipments.${number}.down_times`
    | `materials.${number}.down_times`;

  const { fieldRows: downTimeLinks } = useShiftReportRows(downTimeLinksPath);

  return [
    { label: '', value: '' },
    // @ts-ignore
    ...(downTimes?.reduce((acc: InputSelectOptionProps[], downTime: (typeof downTimes)[number]) => {
      const label = downTime.issue_description;
      const value = downTime.id;

      const usedDownTime =
        downTimeLinks.filter((downTimeLink) => {
          return downTime.id === downTimeLink.allocation_id && downTime.id !== currentDownTimeLink.allocation_id;
        }).length >= 1;

      if (!usedDownTime && label && value) {
        const newOption = { label, value };
        return [...acc, newOption];
      }

      return acc;
    }, []) || []),
  ];
};

type DownTimeLinkFormRowProps = {
  index: number;
  field: ContractForcesItemDownTimesItem | EquipmentsItemDownTimesItem | MaterialsItemDownTimesItem;

  path: Extract<ShiftReportRowPathname, `${string}.${number}.down_times.${number}`>;
};

export const DownTimeLinkFormRow: React.FC<DownTimeLinkFormRowProps> = ({
  field: currentDownTimeLink,
  index: rowIndex,
  path,
}) => {
  const messages = useMessageGetter('shiftReport.form');
  const { user, register, handleDeleteRow } = useShiftReportRow(path);
  const downTimesOptions = useDownTimesAsSelectOptions(path, currentDownTimeLink);

  const pathWithHourField = !path.startsWith('material');

  return (
    <FormRowWithActions rowIndex={rowIndex} user={user} onDelete={handleDeleteRow} deleteIcon={XCircleIcon}>
      <div className="flex w-full gap-x-3">
        <div className="grow">
          <InputSelect
            {...register(`${path}.allocation_id`)}
            options={downTimesOptions}
            label={rowIndex === 0 && messages('downTime')}
          />
        </div>
        {pathWithHourField && (
          <div className="w-1/3 max-w-xs">
            <InputNumber
              {...register(`${path}.quantity`, {
                valueAsNumber: true,
              })}
              label={rowIndex === 0 && messages('hoursSubTotal')}
            />
          </div>
        )}
      </div>
    </FormRowWithActions>
  );
};
