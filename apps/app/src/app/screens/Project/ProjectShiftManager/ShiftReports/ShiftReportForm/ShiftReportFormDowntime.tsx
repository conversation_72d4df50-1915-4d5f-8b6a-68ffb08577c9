import { useMessageGetter } from '@messageformat/react';
import type { IssueSchema } from '@shape-construction/api/src/types';
import { InputNumber, InputText } from '@shape-construction/arch-ui';
import { ClockIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { LinkedIssue } from 'app/components/ShiftManager/Issues/LinkedIssue/LinkedIssue';
import { useProjectIssue } from 'app/queries/issues/issues';
import { useShiftReportRow } from 'app/screens/Project/ProjectShiftManager/ShiftReports/ShiftReportForm/useShiftReportRow';
import { useShiftReportRows } from 'app/screens/Project/ProjectShiftManager/ShiftReports/ShiftReportForm/useShiftReportRows';
import React, { useCallback, useEffect } from 'react';
import { useParams } from 'react-router';
import { FormSectionHeader } from '../components/FormSection';
import { ShiftReportLineItemsDocuments } from '../components/ShiftReportDocuments/ShiftReportLineItemsDocuments';
import { useShiftReportsResourcesDocuments } from '../components/ShiftReportDocuments/hooks/useShiftReportsResourcesDocuments';
import { ShiftReportImportPanel } from '../components/ShiftReportImport/components/ShiftReportImportPanel';
import type { DownTimeItem, ShiftReportRowPathname } from './ShiftReportForm';
import { FormBlock } from './components/FormBlock';
import { IssueSearchField } from './components/IssueSearchField';
import { MultipleFormRows } from './components/MultipleFormRows';

const pathname = 'down_times';

type DowntimeFormRowProps = {
  field: DownTimeItem;
  index: number;
  path: Extract<ShiftReportRowPathname, `down_times.${number}`>;
  last?: boolean;
};

export const DowntimeFormRow: React.FC<DowntimeFormRowProps> = ({ index: rowIndex, field: downtime, path, last }) => {
  const { projectId, shiftReportId } = useParams<{ projectId: string; shiftReportId: string }>();
  const messages = useMessageGetter('shiftReport.form');
  const { user, register, setValue, getValues, submitForm, handleDeleteRow } = useShiftReportRow(path);

  const { data: shiftIssue, isSuccess } = useProjectIssue(projectId!, getValues(`${path}.issue_id`)!, {
    query: {
      enabled: !!projectId && !!getValues(`${path}.issue_id`),
    },
  });

  const prefillDescription = useCallback(
    (linkedIssue: IssueSchema) => {
      if (!getValues(`${path}.issue_description`)) {
        setValue(`${path}.issue_description`, linkedIssue.title, { shouldDirty: true });
      }
    },
    [getValues, path, setValue]
  );

  const linkShiftIssue = (issueId: string) => {
    setValue(`${path}.issue_id`, issueId, {
      shouldDirty: true,
    });
    submitForm();
  };

  const unlinkShiftIssue = () => {
    linkShiftIssue('');
  };

  const { attachmentsBadge, displayDocumentsGallery, documents, deleteDocument } = useShiftReportsResourcesDocuments({
    projectId,
    shiftReportId,
    resource: downtime,
    resourceType: pathname,
    fieldDocumentCount: getValues(`${path}.document_count` as any) || 0,
  });

  const badges = attachmentsBadge ? [attachmentsBadge] : undefined;

  useEffect(() => {
    if (isSuccess) {
      prefillDescription(shiftIssue);
    }
  }, [isSuccess, prefillDescription, shiftIssue]);

  return (
    <FormBlock
      rowIndex={rowIndex}
      user={user}
      onDelete={handleDeleteRow}
      title={downtime.issue_description}
      defaultTitle={messages('downtimeDefaultTitle')}
      badges={badges}
      rowId={downtime.id}
      rowType={pathname}
      resource={downtime}
      path={path}
    >
      <div className="mb-3">
        {shiftIssue && <LinkedIssue issue={shiftIssue} unlinkIssue={unlinkShiftIssue} />}
        {!shiftIssue && <IssueSearchField linkShiftIssue={linkShiftIssue} />}
      </div>
      <div className="grid w-full grid-cols-1 gap-y-2 gap-x-3 lg:grid-cols-12">
        <div className="lg:col-span-6">
          <InputText {...register(`${path}.issue_description`)} label={messages('issueDescription')} />
        </div>
        <div className="flex flex-col gap-y-2 gap-x-3 lg:col-span-6 lg:flex-row">
          <div className="min-w-[100px] lg:max-w-[100px]">
            <InputNumber {...register(`${path}.time_lost`, { valueAsNumber: true })} label={messages('timeLost')} />
          </div>
          <div className="grow">
            <InputText {...register(`${path}.causal_type`)} label={messages('reason')} />
          </div>
        </div>
        {displayDocumentsGallery && (
          <div className="lg:col-span-6 2xl:col-span-12" data-cy={`${pathname}.${rowIndex}.gallery`}>
            <span className="mb-1 block text-sm font-medium text-gray-700">{messages('attachments')}</span>
            <ShiftReportLineItemsDocuments
              documents={documents}
              deleteDocument={(documentId) =>
                deleteDocument({
                  projectId: projectId!,
                  shiftReportId: shiftReportId!,
                  resourceType: pathname,
                  resourceId: downtime.id!,
                  documentId,
                })
              }
              resourceId={downtime.id!}
              resourceType={pathname}
            />
          </div>
        )}
      </div>
    </FormBlock>
  );
};

export const ShiftReportFormDowntime = () => {
  const messages = useMessageGetter('shiftReport.form');
  const { fieldRows, handleAddRow, isAddingRow } = useShiftReportRows(pathname);

  return (
    <fieldset className="grid grid-cols-1 gap-6">
      <div className="flex justify-between">
        <legend className="mt-1">
          <FormSectionHeader>{messages('downTime')}</FormSectionHeader>
        </legend>
        <div className="ml-auto">
          <ShiftReportImportPanel section={pathname} />
        </div>
      </div>
      <MultipleFormRows
        addRowLabel={messages('addDowntime')}
        showEmptyState
        icon={<ClockIcon className="w-12 h-12 lg:w-14 lg:h-14" />}
        emptyDescription={messages('downtimeDescription')}
        fields={fieldRows}
        formRowComponent={DowntimeFormRow}
        onAddRow={handleAddRow}
        isAddingRow={isAddingRow}
        path={pathname}
      />
    </fieldset>
  );
};
