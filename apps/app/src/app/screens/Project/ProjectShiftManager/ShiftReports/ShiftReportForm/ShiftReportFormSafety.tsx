import { useMessageGetter } from '@messageformat/react';
import { InputText } from '@shape-construction/arch-ui';
import { ShieldCheckIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { useShiftReportRow } from 'app/screens/Project/ProjectShiftManager/ShiftReports/ShiftReportForm/useShiftReportRow';
import { useShiftReportRows } from 'app/screens/Project/ProjectShiftManager/ShiftReports/ShiftReportForm/useShiftReportRows';
import React, { memo } from 'react';
import { useParams } from 'react-router';
import { FormSectionHeader } from '../components/FormSection';
import { ShiftReportLineItemsDocuments } from '../components/ShiftReportDocuments/ShiftReportLineItemsDocuments';
import { useShiftReportsResourcesDocuments } from '../components/ShiftReportDocuments/hooks/useShiftReportsResourcesDocuments';
import type { SafetyHealthEnvironmentItem, ShiftReportRowPathname } from './ShiftReportForm';
import { FormBlock } from './components/FormBlock';
import { MultipleFormRows } from './components/MultipleFormRows';

const pathname = 'safety_health_environments';

type SafetyFormRowProps = {
  field: SafetyHealthEnvironmentItem;
  index: number;
  path: Extract<ShiftReportRowPathname, `safety_health_environments.${number}`>;
  last?: boolean;
};

export const SafetyFormRow: React.FC<SafetyFormRowProps> = ({ field: safetyNote, index: rowIndex, path, last }) => {
  const { projectId, shiftReportId } = useParams<{ projectId: string; shiftReportId: string }>();
  const messages = useMessageGetter('shiftReport.form');
  const { user, register, handleDeleteRow, getValues } = useShiftReportRow(path);

  const { attachmentsBadge, displayDocumentsGallery, documents, deleteDocument } = useShiftReportsResourcesDocuments({
    projectId,
    shiftReportId,
    resource: safetyNote,
    resourceType: pathname,
    fieldDocumentCount: getValues(`${path}.document_count` as any) || 0,
  });

  const badges = attachmentsBadge ? [attachmentsBadge] : undefined;

  return (
    <FormBlock
      rowIndex={rowIndex}
      user={user}
      onDelete={handleDeleteRow}
      title={safetyNote.safety_note}
      defaultTitle={messages('safetyNoteDefaultTitle')}
      badges={badges}
      rowId={safetyNote.id}
      rowType={pathname}
      resource={safetyNote}
      path={path}
    >
      <div className="w-full flex flex-col gap-y-2">
        <InputText {...register(`${path}.safety_note`)} label={messages('safetyNote')} />
        {displayDocumentsGallery && (
          <div data-cy={`${pathname}.${rowIndex}.gallery`}>
            <span className="mb-1 block text-sm font-medium text-gray-700">{messages('attachments')}</span>
            <ShiftReportLineItemsDocuments
              documents={documents}
              deleteDocument={(documentId) =>
                deleteDocument({
                  projectId: projectId!,
                  shiftReportId: shiftReportId!,
                  resourceType: pathname,
                  resourceId: safetyNote.id!,
                  documentId,
                })
              }
              resourceId={safetyNote.id!}
              resourceType={pathname}
            />
          </div>
        )}
      </div>
    </FormBlock>
  );
};

export const ShiftReportFormSafety = memo(() => {
  const messages = useMessageGetter('shiftReport.form');
  const { fieldRows, handleAddRow, isAddingRow } = useShiftReportRows(pathname);

  return (
    <fieldset className="grid grid-cols-1 gap-6">
      <legend>
        <FormSectionHeader>{messages('safetyHealthEnvironment')}</FormSectionHeader>
      </legend>
      <MultipleFormRows
        addRowLabel={messages('addSafetyNote')}
        showEmptyState
        icon={<ShieldCheckIcon className="w-12 h-12 lg:w-14 lg:h-14" />}
        emptyDescription={messages('safetyDescription')}
        fields={fieldRows}
        formRowComponent={SafetyFormRow}
        onAddRow={handleAddRow}
        isAddingRow={isAddingRow}
        path={pathname}
      />
    </fieldset>
  );
});
