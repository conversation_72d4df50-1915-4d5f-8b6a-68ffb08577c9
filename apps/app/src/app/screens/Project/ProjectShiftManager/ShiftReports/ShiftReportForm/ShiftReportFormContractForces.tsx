import { useMessageGetter } from '@messageformat/react';
import type { ShiftReportContractForceSchema } from '@shape-construction/api/src';
import { InputNumber, InputTextArea } from '@shape-construction/arch-ui';
import { THEME } from '@shape-construction/arch-ui/src/Badge/Badge.types';
import { UserGroupIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { PlusCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { useShiftReportRow } from 'app/screens/Project/ProjectShiftManager/ShiftReports/ShiftReportForm/useShiftReportRow';
import { useShiftReportRows } from 'app/screens/Project/ProjectShiftManager/ShiftReports/ShiftReportForm/useShiftReportRows';
import React, { useMemo } from 'react';
import { Controller } from 'react-hook-form';
import { useParams } from 'react-router';
import { FormSectionHeader } from '../components/FormSection';
import { ShiftReportLineItemsDocuments } from '../components/ShiftReportDocuments/ShiftReportLineItemsDocuments';
import { useShiftReportsResourcesDocuments } from '../components/ShiftReportDocuments/hooks/useShiftReportsResourcesDocuments';
import { ShiftReportImportPanel } from '../components/ShiftReportImport/components/ShiftReportImportPanel';
import { ActivityLinkFormRow } from './ActivityLinkFormRow';
import { DownTimeLinkFormRow } from './DownTimeLinkFormRow';
import {
  type ContractForceItem,
  type ShiftReportRowPathname,
  type ShiftReportRowsPathname,
  useShiftReportFormContext,
} from './ShiftReportForm';
import { FormBlock } from './components/FormBlock';
import { FormBlockActions } from './components/FormBlockActions';
import { MultipleFormRows } from './components/MultipleFormRows';
import { ResourceSearchField } from './components/ResourceSearchField';
import { useResourceLinks } from './useResourceLinks';

const pathname = 'contract_forces';

type ContractForcesFormRowProps = {
  field: ContractForceItem & Partial<Pick<ShiftReportContractForceSchema, 'name' | 'role' | 'organisation'>>;
  index: number;
  path: Extract<ShiftReportRowPathname, `contract_forces.${number}`>;
  last?: boolean;
};

export const ContractForcesFormRow: React.FC<ContractForcesFormRowProps> = ({
  field: contractForce,
  index: rowIndex,
  path,
  last,
}) => {
  const messages = useMessageGetter('shiftReport.form');
  const { projectId, shiftReportId } = useParams<{ projectId: string; shiftReportId: string }>();
  const { control, submitForm } = useShiftReportFormContext();
  const { user, register, handleDeleteRow, getValues } = useShiftReportRow(path);
  const { activities: activityLinks, down_times: downTimeLinks } = contractForce;

  const activitiesPath: ShiftReportRowsPathname = `${path}.activities`;
  const {
    fieldRows: activityRows,
    handleAddRow: handleAddActivityRow,
    isAddingRow: isAddingActivityRow,
  } = useShiftReportRows(activitiesPath);

  const downTimesPath: ShiftReportRowsPathname = `${path}.down_times`;
  const {
    fieldRows: downTimeRows,
    handleAddRow: handleAddDownTimeRow,
    isAddingRow: isAddingDownTimeRow,
  } = useShiftReportRows(downTimesPath);

  const { activityLinksNum, downTimeLinksNum, disableHours } = useResourceLinks({
    activityLinks,
    downTimeLinks,
    rowIndex,
    path,
  });

  const { attachmentsBadge, displayDocumentsGallery, documents, deleteDocument } = useShiftReportsResourcesDocuments({
    projectId,
    shiftReportId,
    resource: contractForce,
    resourceType: pathname,
    fieldDocumentCount: getValues(`${path}.document_count` as any) || 0,
  });

  const activityLinkLabel = messages('allocateProgress');
  const activityBadgeLabel = messages('progressBadge', { activityLinksNum });
  const allocateDowntimeLabel = messages('allocateDowntime');

  const badges = useMemo(
    () =>
      [
        {
          theme: THEME.GREEN,
          label: contractForce.hours ? `${contractForce.hours ?? 0}h` : '',
        },
        {
          theme: THEME.BLUE,
          label: activityLinksNum > 0 ? activityBadgeLabel : '',
        },
        {
          theme: THEME.PINK,
          label: downTimeLinksNum > 0 ? messages('downTimeBadge', { downTimeLinksNum }) : '',
        },
        attachmentsBadge || { label: '' },
      ].filter((b) => !!b.label),
    [activityLinksNum, downTimeLinksNum, contractForce.hours, messages, activityBadgeLabel, attachmentsBadge]
  );

  const allocateActions = [
    {
      label: activityLinkLabel,
      onClick: () => handleAddActivityRow(),
      icon: PlusCircleIcon,
      isLoading: isAddingActivityRow,
    },
    {
      label: allocateDowntimeLabel,
      onClick: () => handleAddDownTimeRow(),
      icon: PlusCircleIcon,
      isLoading: isAddingDownTimeRow,
    },
  ];

  return (
    <div className="flex gap-x-2" data-testid={path}>
      <FormBlock
        rowIndex={rowIndex}
        user={user}
        onDelete={handleDeleteRow}
        title={contractForce.name}
        defaultTitle={messages('peopleDefaultTitle')}
        badges={badges}
        actions={<FormBlockActions triggerLabel={messages('allocateTriggerLabel')} items={allocateActions} />}
        rowId={contractForce.id}
        rowType={pathname}
        resource={contractForce}
        path={path}
      >
        <div className="grid w-full grid-cols-1 gap-y-2 lg:gap-x-3 lg:grid-cols-8">
          <div className="col-span-1 lg:col-span-4 grid grid-cols-1 lg:grid-cols-2  gap-y-2 gap-x-3 ">
            <div className="grow">
              <Controller
                control={control}
                name={`${path}.person_resource_id`}
                render={({ field }) => (
                  <ResourceSearchField
                    kind="person"
                    label={messages('name')}
                    value={field.value}
                    onChange={field.onChange}
                    onBlur={submitForm}
                    name={`${path}.person_resource_id`}
                    displayName={contractForce?.name}
                  />
                )}
              />
            </div>
            <div className="grow">
              <Controller
                control={control}
                name={`${path}.role_resource_id`}
                render={({ field }) => (
                  <ResourceSearchField
                    kind="role"
                    label={messages('role')}
                    value={field.value}
                    onChange={field.onChange}
                    onBlur={submitForm}
                    name={`${path}.role_resource_id`}
                    displayName={contractForce?.role}
                  />
                )}
              />
            </div>
            <div className="grow">
              <Controller
                control={control}
                name={`${path}.organisation_resource_id`}
                render={({ field }) => (
                  <ResourceSearchField
                    kind="organisation"
                    label={messages('organisation')}
                    value={field.value}
                    onChange={field.onChange}
                    onBlur={submitForm}
                    name={`${path}.organisation_resource_id`}
                    displayName={contractForce?.organisation}
                  />
                )}
              />
            </div>
            <div className="w-[100px]">
              <InputNumber
                {...register(`${path}.hours`, {
                  valueAsNumber: true,
                })}
                disabled={disableHours}
                label={messages('hours')}
              />
            </div>
          </div>
          <div className="col-span-4 flex flex-row gap-y-2 gap-x-3 lg:col-span-4 bg-blue h-full">
            <div className="grow [&_label]:h-full [&_label]:flex [&_label]:flex-col [&_label_div.relative]:grow">
              <InputTextArea {...register(`${path}.comment`)} label={messages('comment')} className="grow h-full" />
            </div>
          </div>
          {displayDocumentsGallery && (
            <div className="lg:col-span-6 2xl:col-span-12" data-cy={`${pathname}.${rowIndex}.gallery`}>
              <span className="mb-1 block text-sm font-medium text-gray-700">{messages('attachments')}</span>
              <ShiftReportLineItemsDocuments
                documents={documents}
                deleteDocument={(documentId) =>
                  deleteDocument({
                    projectId: projectId!,
                    shiftReportId: shiftReportId!,
                    resourceType: pathname,
                    resourceId: contractForce.id!,
                    documentId,
                  })
                }
                resourceId={contractForce.id!}
                resourceType={pathname}
              />
            </div>
          )}
        </div>
        <MultipleFormRows
          textButton
          addRowLabel={activityLinkLabel}
          fields={activityRows}
          formRowComponent={ActivityLinkFormRow}
          onAddRow={handleAddActivityRow}
          isAddingRow={isAddingActivityRow}
          path={activitiesPath}
          hideAddRow={activityRows.length === 0}
        />
        <MultipleFormRows
          textButton
          addRowLabel={allocateDowntimeLabel}
          fields={downTimeRows}
          formRowComponent={DownTimeLinkFormRow}
          onAddRow={handleAddDownTimeRow}
          isAddingRow={isAddingDownTimeRow}
          path={downTimesPath}
          hideAddRow={downTimeRows.length === 0}
        />
      </FormBlock>
    </div>
  );
};

export const ShiftReportFormContractForces = () => {
  const messages = useMessageGetter('shiftReport.form');
  const { fieldRows, handleAddRow, isAddingRow } = useShiftReportRows(pathname);

  return (
    <fieldset className="grid grid-cols-1 gap-6">
      <div className="flex justify-between">
        <legend className="mt-1">
          <FormSectionHeader>{messages('people')}</FormSectionHeader>
        </legend>
        <div className="ml-auto">
          <ShiftReportImportPanel section={pathname} />
        </div>
      </div>
      <MultipleFormRows
        addRowLabel={messages('addPerson')}
        showEmptyState
        icon={<UserGroupIcon className="w-12 h-12 lg:w-14 lg:h-14" />}
        emptyDescription={messages('peopleDescription')}
        emptySubDescription={messages('peopleSubDescription')}
        fields={fieldRows}
        formRowComponent={ContractForcesFormRow}
        onAddRow={handleAddRow}
        isAddingRow={isAddingRow}
        path={pathname}
      />
    </fieldset>
  );
};
