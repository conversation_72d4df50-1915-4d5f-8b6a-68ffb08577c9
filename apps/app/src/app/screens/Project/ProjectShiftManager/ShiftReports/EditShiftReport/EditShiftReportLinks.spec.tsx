import { activityFactory } from '@shape-construction/api/factories/activities';
import { projectAvailableActions, projectFactory } from '@shape-construction/api/factories/projects';
import {
  shiftReportsActivityFactory,
  shiftReportsAvailableActions,
  shiftReportsContractForceFactory,
  shiftReportsEquipmentFactory,
  shiftReportsFactory,
  shiftReportsLinkFactory,
  shiftReportsMaterialFactory,
} from '@shape-construction/api/factories/shiftReports';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { getApiProjectsProjectIdShiftActivitiesShiftActivityIdMockHandler } from '@shape-construction/api/handlers-factories/projects/shift-activities';
import {
  getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler,
  patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler,
} from '@shape-construction/api/handlers-factories/projects/shift-reports';
import { createMemoryHistory } from 'history';
import React from 'react';
import { server } from 'tests/mock-server';
import { render, screen, userEvent, waitFor, within } from 'tests/test-utils';
import { EditShiftReport } from './EditShiftReport';

describe('EditShiftReportLinks', () => {
  describe('when users adds a links to people', () => {
    it('adds an activity link', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        contractForces: [shiftReportsContractForceFactory({ id: 'contract-force-id' })],
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      const patchApiMock = jest.fn();
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
        patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(async ({ request }) => {
          patchApiMock(await request.json());
          return shiftReport;
        })
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };
      render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });
      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());
      expect(await screen.findByText('shiftReport.form.people')).toBeInTheDocument();
      const peopleRow = within(screen.getByTestId('contract_forces.0'));
      expect(peopleRow.queryAllByRole('button', { name: 'shiftReport.form.delete' })).toHaveLength(2);

      await userEvent.click(
        await peopleRow.findByRole('button', {
          hidden: false,
          name: 'Form block action shiftReport.form.allocateProgress',
        })
      );

      await waitFor(() =>
        expect(patchApiMock).toHaveBeenCalledWith({
          contract_forces: [{ activities: [{}], id: shiftReport.contractForces[0].id }],
        })
      );
    });

    it('adds a downTime link', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
        contractForces: [
          shiftReportsContractForceFactory({
            activities: undefined,
            downTimes: [shiftReportsLinkFactory()],
          }),
        ],
      });
      const patchApiMock = jest.fn();
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
        patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(async ({ request }) => {
          patchApiMock(await request.json());
          return shiftReport;
        })
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };
      render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });
      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());
      expect(await screen.findByText('shiftReport.form.people')).toBeInTheDocument();
      const peopleRow = within(screen.getByTestId('contract_forces.0'));
      expect(peopleRow.queryAllByRole('button', { name: 'shiftReport.form.delete' })).toHaveLength(2);

      await userEvent.click(
        await peopleRow.findByRole('button', {
          hidden: false,
          name: 'Form block action shiftReport.form.allocateDowntime',
        })
      );

      // await waitFor(() =>
      //   expect(patchApiMock).toHaveBeenCalledWith({
      //     contract_forces: [{ down_times: [{}], id: shiftReport.contractForces[0].id }],
      //   })
      // );
    });
  });

  describe('when users adds an activity links to equipments', () => {
    it('adds an activity link', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        equipments: [shiftReportsEquipmentFactory({ id: 'equipment-id', activities: [shiftReportsLinkFactory()] })],
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      const patchApiMock = jest.fn();
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
        patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(async ({ request }) => {
          patchApiMock(await request.json());
          return shiftReport;
        }),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport)
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };
      render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });
      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());
      expect(await screen.findByText('shiftReport.form.equipment')).toBeInTheDocument();
      const equipmentRow = within(screen.getByTestId('equipments.0'));
      expect(equipmentRow.queryAllByRole('button', { name: 'shiftReport.form.delete' })).toHaveLength(2);

      await userEvent.click(
        await equipmentRow.findByRole('button', {
          hidden: false,
          name: 'Form block action shiftReport.form.allocateProgress',
        })
      );

      await waitFor(() =>
        expect(patchApiMock).toHaveBeenCalledWith({
          equipments: [{ activities: [{}], id: shiftReport.equipments[0].id }],
        })
      );
    });

    it('adds a downTime link', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
        equipments: [
          shiftReportsEquipmentFactory({
            activities: undefined,
            downTimes: [shiftReportsLinkFactory()],
          }),
        ],
      });
      const patchApiMock = jest.fn();
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
        patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(async ({ request }) => {
          patchApiMock(await request.json());
          return shiftReport;
        }),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport)
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };
      render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });
      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());
      await screen.findByText('shiftReport.form.equipment');
      const equipmentRow = within(screen.getByTestId('equipments.0'));
      expect(equipmentRow.queryAllByRole('button', { name: 'shiftReport.form.delete' })).toHaveLength(2);

      await userEvent.click(
        await equipmentRow.findByRole('button', {
          hidden: false,
          name: 'Form block action shiftReport.form.allocateDowntime',
        })
      );

      await waitFor(() =>
        expect(patchApiMock).toHaveBeenCalledWith({
          equipments: [{ down_times: [{}], id: shiftReport.equipments[0].id }],
        })
      );
    });
  });

  describe('when users adds an activity links to materials', () => {
    it('adds an activity link', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      const patchApiMock = jest.fn();
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
        patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(async ({ request }) => {
          patchApiMock(await request.json());
          return shiftReport;
        }),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport)
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };
      render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });
      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());
      await screen.findByText('shiftReport.form.material');
      const materialRow = within(screen.getByTestId('materials.0'));
      expect(materialRow.queryAllByRole('button', { name: 'shiftReport.form.delete' })).toHaveLength(2);
      await userEvent.click(
        await materialRow.findByRole('button', {
          hidden: false,
          name: 'Form block action shiftReport.form.allocateProgress',
        })
      );

      await waitFor(() =>
        expect(patchApiMock).toHaveBeenCalledWith({
          materials: [{ activities: [{}], id: shiftReport.materials[0].id }],
        })
      );
    });

    it('adds a downTime link', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
        materials: [
          shiftReportsMaterialFactory({
            activities: undefined,
            downTimes: [shiftReportsLinkFactory()],
          }),
        ],
      });
      const patchApiMock = jest.fn();
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
        patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(async ({ request }) => {
          patchApiMock(await request.json());
          return shiftReport;
        }),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport)
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };
      render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });
      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());
      await screen.findByText('shiftReport.form.material');
      const materialRow = within(screen.getByTestId('materials.0'));
      expect(materialRow.queryAllByRole('button', { name: 'shiftReport.form.delete' })).toHaveLength(2);

      await userEvent.click(
        await materialRow.findByRole('button', {
          hidden: false,
          name: 'Form block action shiftReport.form.allocateDowntime',
        })
      );

      await waitFor(() =>
        expect(patchApiMock).toHaveBeenCalledWith({
          materials: [{ down_times: [{}], id: shiftReport.materials[0].id }],
        })
      );
    });
  });

  describe('when a row with links is collapsed', () => {
    it('shows activity links badge', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport)
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

      render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });

      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());

      const peopleRow = within(screen.getByTestId('contract_forces.0'));
      await userEvent.click(await peopleRow.findByRole('button', { name: 'shiftReport.form.closeRowLinks' }));

      expect(await peopleRow.findByRole('button', { name: 'shiftReport.form.openRowLinks' })).toBeInTheDocument();
      expect(await peopleRow.findByText('shiftReport.form.progressBadge')).toBeInTheDocument();
    });

    describe('when the user clicks on collapse row button', () => {
      it('closes row links form', async () => {
        const project = projectFactory({
          id: 'project-0',
          availableActions: projectAvailableActions({
            createShiftReport: true,
          }),
        });
        const shiftReport = shiftReportsFactory({
          availableActions: shiftReportsAvailableActions({
            edit: true,
            editRootFields: true,
          }),
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport)
        );
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
        });
        const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

        render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });

        expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
        await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());

        expect(screen.getAllByText('shiftReport.form.allocateProgress').length).toBe(6);

        const equipmentsRow = within(screen.getByTestId('equipments.0'));
        await userEvent.click(await equipmentsRow.findByRole('button', { name: 'shiftReport.form.closeRowLinks' }));

        expect(screen.getAllByText('shiftReport.form.allocateProgress').length).toBe(4);
      });
    });
  });

  describe('when a row with links is open', () => {
    describe('when the user clicks on collapse row button', () => {
      it('closes row links form', async () => {
        const project = projectFactory({
          id: 'project-0',
          availableActions: projectAvailableActions({
            createShiftReport: true,
          }),
        });
        const shiftReport = shiftReportsFactory({
          availableActions: shiftReportsAvailableActions({
            edit: true,
            editRootFields: true,
          }),
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport)
        );
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
        });
        const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

        render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });

        expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
        await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());

        expect(screen.getAllByText('shiftReport.form.allocateProgress').length).toBe(6);

        const equipmentsRow = within(screen.getByTestId('equipments.0'));
        await userEvent.click(await equipmentsRow.findByRole('button', { name: 'shiftReport.form.closeRowLinks' }));

        expect(screen.getAllByText('shiftReport.form.allocateProgress').length).toBe(4);

        await userEvent.click(await equipmentsRow.findByRole('button', { name: 'shiftReport.form.openRowLinks' }));

        expect(screen.getAllByText('shiftReport.form.allocateProgress').length).toBe(6);
      });
    });
  });

  describe('when a user adds quantity to a activity link', () => {
    it('shows activity link hours quantity in the person hours field', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      const patchedShiftReportOne = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
        contractForces: [
          shiftReportsContractForceFactory({
            activities: [shiftReportsLinkFactory({ quantity: 11 })],
          }),
        ],
      });
      const patchedShiftReportTwo = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
        contractForces: [
          shiftReportsContractForceFactory({
            hours: 11,
            activities: [shiftReportsLinkFactory({ quantity: 11 })],
          }),
        ],
      });

      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
        patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => patchedShiftReportOne, undefined, {
          once: true,
        }),
        patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => patchedShiftReportTwo, undefined, {
          once: true,
        })
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

      render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });

      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());

      const peopleRow = within(screen.getByTestId('contract_forces.0'));

      await userEvent.clear(peopleRow.getByLabelText('shiftReport.form.hoursSubTotal'));
      await userEvent.type(peopleRow.getByLabelText('shiftReport.form.hoursSubTotal'), '11');
      await userEvent.tab();

      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();

      await waitFor(() => expect(peopleRow.getByLabelText('shiftReport.form.hoursSubTotal')).toHaveValue(11));

      await waitFor(() => expect(peopleRow.getByLabelText('shiftReport.form.hours')).toHaveValue(11));
    });
  });

  describe('Linked activities', () => {
    describe('when there is no linked activity', () => {
      it('renders the search activity input', async () => {
        const project = projectFactory({
          id: 'project-0',
          availableActions: projectAvailableActions({
            createShiftReport: true,
          }),
        });
        const shiftReport = shiftReportsFactory({
          availableActions: shiftReportsAvailableActions({
            edit: true,
            editRootFields: true,
          }),
          activities: [
            shiftReportsActivityFactory({
              shiftActivityId: null,
            }),
          ],
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport)
        );
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
        });
        const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };
        render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });

        expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
        await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());
        expect(await screen.findByText('shiftReport.form.activity')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('shiftReport.form.searchActivityPlaceholder')).toBeInTheDocument();
        await waitFor(() => expect(screen.queryByTestId('linked-activity-container')).not.toBeInTheDocument());
      });
    });

    describe("when there's a linked activity", () => {
      it('unlinks the activity', async () => {
        const project = projectFactory({
          id: 'project-0',
          availableActions: projectAvailableActions({
            createShiftReport: true,
          }),
        });
        const shiftReport = shiftReportsFactory({
          availableActions: shiftReportsAvailableActions({
            edit: true,
            editRootFields: true,
          }),
          activities: [
            shiftReportsActivityFactory({
              id: 'shiftReportActivity-0',
              shiftActivityId: 'shiftActivity-0',
            }),
          ],
        });
        const shiftActivity = activityFactory({
          id: 'shiftActivity-0',
          description: 'This is the shiftActivity-0 description',
        });
        const patchedShiftReport = shiftReportsFactory({
          availableActions: shiftReportsAvailableActions({
            edit: true,
            editRootFields: true,
          }),
          activities: [
            shiftReportsActivityFactory({
              id: 'shiftReportActivity-0',
              shiftActivityId: null,
            }),
          ],
        });
        server.use(
          patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => patchedShiftReport),
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
          getApiProjectsProjectIdShiftActivitiesShiftActivityIdMockHandler(() => shiftActivity)
        );
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
        });
        const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };
        render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });

        expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();

        const linkedActivityContainer = await screen.findByTestId('linked-activity-container');
        expect(linkedActivityContainer).toBeInTheDocument();
        expect(
          within(linkedActivityContainer).getByText('This is the shiftActivity-0 description')
        ).toBeInTheDocument();

        await userEvent.click(
          within(linkedActivityContainer).getByRole('button', {
            name: 'shiftReport.form.unlinkActivityCTA',
          })
        );

        expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();

        expect(await screen.findByPlaceholderText('shiftReport.form.searchActivityPlaceholder')).toBeInTheDocument();

        await waitFor(() => expect(screen.queryByTestId('linked-activity-container')).not.toBeInTheDocument());
      });
    });
  });
});
