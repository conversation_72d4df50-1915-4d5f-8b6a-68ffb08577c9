import { useMessageGetter } from '@messageformat/react';
import type { PatchApiProjectsProjectIdShiftReportsShiftReportIdMutationRequestSchema } from '@shape-construction/api/src/types';
import { InputNumber } from '@shape-construction/arch-ui';
import { XCircleIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { InputSelect, type InputSelectOptionProps } from '@shape-construction/arch-ui/src/InputSelect/InputSelect';
import React from 'react';
import type { ShiftReportRowPathname } from './ShiftReportForm';
import { FormRowWithActions } from './components/FormRowWithActions';
import { getArrayPathFromArrayItemPath } from './formUtils';
import { useShiftReportRow } from './useShiftReportRow';
import { useShiftReportRows } from './useShiftReportRows';

type ContractForcesItemActivitiesItem = NonNullable<
  NonNullable<
    PatchApiProjectsProjectIdShiftReportsShiftReportIdMutationRequestSchema['contract_forces']
  >[number]['activities']
>[number];

type EquipmentsItemActivitiesItem = NonNullable<
  NonNullable<
    PatchApiProjectsProjectIdShiftReportsShiftReportIdMutationRequestSchema['equipments']
  >[number]['activities']
>[number];
type MaterialsItemActivitiesItem = NonNullable<
  NonNullable<
    PatchApiProjectsProjectIdShiftReportsShiftReportIdMutationRequestSchema['materials']
  >[number]['activities']
>[number];

export const useActivitiesAsSelectOptions = (
  path: ActivityLinkFormRowProps['path'],
  currentActivityLink: ContractForcesItemActivitiesItem
): InputSelectOptionProps[] => {
  const { fieldRows: activities } = useShiftReportRows('activities');
  const activityLinksPath = getArrayPathFromArrayItemPath(path) as
    | `contract_forces.${number}.activities`
    | `equipments.${number}.activities`
    | `materials.${number}.activities`;

  const { fieldRows: activityLinks } = useShiftReportRows(activityLinksPath);

  return [
    { label: '', value: '' },
    // @ts-ignore
    ...(activities?.reduce((acc: InputSelectOptionProps[], activity: (typeof activities)[number]) => {
      const label = activity.description;
      const value = activity.id;

      const usedActivity =
        activityLinks.filter((activityLink) => {
          return activity.id === activityLink.allocation_id && activity.id !== currentActivityLink.allocation_id;
        }).length >= 1;

      if (!usedActivity && label && value) {
        const newOption = { label, value };
        return [...acc, newOption];
      }

      return acc;
    }, []) || []),
  ];
};

type ActivityLinkFormRowProps = {
  index: number;
  field: ContractForcesItemActivitiesItem | EquipmentsItemActivitiesItem | MaterialsItemActivitiesItem;
  path: Extract<ShiftReportRowPathname, `${string}.${number}.activities.${number}`>;
  last?: boolean;
};

export const ActivityLinkFormRow: React.FC<ActivityLinkFormRowProps> = ({
  field: currentActivityLink,
  index: rowIndex,
  path,
  last,
}) => {
  const messages = useMessageGetter('shiftReport.form');
  const { user, register, handleDeleteRow } = useShiftReportRow(path);
  const activitiesOptions = useActivitiesAsSelectOptions(path, currentActivityLink);

  const pathWithHourField = !path.startsWith('material');

  return (
    <FormRowWithActions rowIndex={rowIndex} user={user} onDelete={handleDeleteRow} deleteIcon={XCircleIcon}>
      <div className="flex w-full gap-x-3">
        <div className="grow">
          <InputSelect
            {...register(`${path}.allocation_id`)}
            options={activitiesOptions}
            label={rowIndex === 0 && messages('progress')}
          />
        </div>
        {pathWithHourField && (
          <div className="w-1/3 max-w-xs">
            <InputNumber
              {...register(`${path}.quantity`, {
                valueAsNumber: true,
              })}
              label={rowIndex === 0 && messages('hoursSubTotal')}
            />
          </div>
        )}
      </div>
    </FormRowWithActions>
  );
};
