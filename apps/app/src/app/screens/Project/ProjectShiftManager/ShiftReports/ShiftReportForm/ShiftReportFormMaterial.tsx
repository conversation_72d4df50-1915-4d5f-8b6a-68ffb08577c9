import { useMessageGetter } from '@messageformat/react';
import type { ShiftReportMaterialSchema } from '@shape-construction/api/src';
import { InputNumber, InputSelect } from '@shape-construction/arch-ui';
import { THEME } from '@shape-construction/arch-ui/src/Badge/Badge.types';
import { ShoppingCartIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { PlusCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { useShiftReportRow } from 'app/screens/Project/ProjectShiftManager/ShiftReports/ShiftReportForm/useShiftReportRow';
import { useShiftReportRows } from 'app/screens/Project/ProjectShiftManager/ShiftReports/ShiftReportForm/useShiftReportRows';
import React, { useMemo } from 'react';
import { Controller } from 'react-hook-form';
import { useParams } from 'react-router';
import { FormSectionHeader } from '../components/FormSection';
import { ShiftReportLineItemsDocuments } from '../components/ShiftReportDocuments/ShiftReportLineItemsDocuments';
import { useShiftReportsResourcesDocuments } from '../components/ShiftReportDocuments/hooks/useShiftReportsResourcesDocuments';
import { ActivityLinkFormRow } from './ActivityLinkFormRow';
import { DownTimeLinkFormRow } from './DownTimeLinkFormRow';
import {
  type MaterialItem,
  type ShiftReportRowPathname,
  type ShiftReportRowsPathname,
  unitOptions,
  useShiftReportFormContext,
} from './ShiftReportForm';
import { FormBlock } from './components/FormBlock';
import { FormBlockActions } from './components/FormBlockActions';
import { MultipleFormRows } from './components/MultipleFormRows';
import { ResourceSearchField } from './components/ResourceSearchField';
import { useResourceLinks } from './useResourceLinks';

const pathname = 'materials';

type MaterialFormRowProps = {
  field: MaterialItem & Partial<Pick<ShiftReportMaterialSchema, 'description'>>;
  index: number;
  path: Extract<ShiftReportRowPathname, `materials.${number}`>;
  last?: boolean;
};

export const MaterialFormRow: React.FC<MaterialFormRowProps> = ({ field: material, index: rowIndex, path, last }) => {
  const messages = useMessageGetter('shiftReport.form');
  const { projectId, shiftReportId } = useParams<{ projectId: string; shiftReportId: string }>();
  const { control, submitForm } = useShiftReportFormContext();
  const { user, register, handleDeleteRow, getValues } = useShiftReportRow(path);

  const { activities: activityLinks, down_times: downTimeLinks } = material;

  const activitiesPath: ShiftReportRowsPathname = `${path}.activities`;
  const {
    fieldRows: activityRows,
    handleAddRow: handleAddActivityRow,
    isAddingRow: isAddingActivityRow,
  } = useShiftReportRows(activitiesPath);

  const downTimesPath: ShiftReportRowsPathname = `${path}.down_times`;
  const {
    fieldRows: downTimeRows,
    handleAddRow: handleAddDownTimeRow,
    isAddingRow: isAddingDownTimeRow,
  } = useShiftReportRows(downTimesPath);

  const { activityLinksNum, downTimeLinksNum } = useResourceLinks({
    activityLinks,
    downTimeLinks,
    rowIndex,
    path,
  });

  const { attachmentsBadge, displayDocumentsGallery, documents, deleteDocument } = useShiftReportsResourcesDocuments({
    projectId,
    shiftReportId,
    resource: material,
    resourceType: pathname,
    fieldDocumentCount: getValues(`${path}.document_count` as any) || 0,
  });

  const activityLinkLabel = messages('allocateProgress');
  const activityBadgeLabel = messages('progressBadge', { activityLinksNum });
  const allocateDowntimeLabel = messages('allocateDowntime');

  const badges = useMemo(
    () =>
      [
        {
          theme: THEME.GREEN,
          label: material?.units ? `${material?.quantity ?? 0} ${material?.units}` : '',
        },
        {
          theme: THEME.BLUE,
          label: activityLinksNum > 0 ? activityBadgeLabel : '',
        },
        {
          theme: THEME.PINK,
          label: downTimeLinksNum > 0 ? messages('downTimeBadge', { downTimeLinksNum }) : '',
        },
        attachmentsBadge || { label: '' },
      ].filter((b) => !!b.label),
    [activityLinksNum, downTimeLinksNum, material, messages, activityBadgeLabel, attachmentsBadge]
  );

  const allocateActions = [
    {
      label: activityLinkLabel,
      onClick: () => handleAddActivityRow(),
      icon: PlusCircleIcon,
      isLoading: isAddingActivityRow,
    },
    {
      label: allocateDowntimeLabel,
      onClick: () => handleAddDownTimeRow(),
      icon: PlusCircleIcon,
      isLoading: isAddingDownTimeRow,
    },
  ];

  return (
    <div className="flex gap-x-2" data-testid={path}>
      <FormBlock
        rowIndex={rowIndex}
        user={user}
        onDelete={handleDeleteRow}
        title={material.description}
        defaultTitle={messages('materialDefaultTitle')}
        badges={badges}
        actions={<FormBlockActions triggerLabel={messages('allocateTriggerLabel')} items={allocateActions} />}
        rowId={material.id}
        rowType={pathname}
        resource={material}
        path={path}
      >
        <div className="flex w-full flex-col gap-y-2 gap-x-3 md:flex-row">
          <div className="grow">
            <Controller
              control={control}
              name={`${path}.material_resource_id`}
              render={({ field }) => (
                <ResourceSearchField
                  kind="material"
                  label={messages('description')}
                  value={field.value}
                  onChange={field.onChange}
                  onBlur={submitForm}
                  name={`${path}.material_resource_id`}
                  displayName={material?.description}
                />
              )}
            />
          </div>
          <div className="flex flex-col gap-y-2 gap-x-3 md:flex-row">
            <div className="min-w-[100px] md:max-w-[100px]">
              <InputNumber {...register(`${path}.quantity`, { valueAsNumber: true })} label={messages('quantity')} />
            </div>
            <div className="min-w-[144px]">
              <InputSelect {...register(`${path}.units`)} label={messages('units')} options={unitOptions} />
            </div>
          </div>
        </div>

        {displayDocumentsGallery && (
          <div className="mt-2" data-cy={`${pathname}.${rowIndex}.gallery`}>
            <span className="mb-1 block text-sm font-medium text-gray-700">{messages('attachments')}</span>
            <ShiftReportLineItemsDocuments
              documents={documents}
              deleteDocument={(documentId) =>
                deleteDocument({
                  projectId: projectId!,
                  shiftReportId: shiftReportId!,
                  resourceType: pathname,
                  resourceId: material.id!,
                  documentId,
                })
              }
              resourceId={material.id!}
              resourceType={pathname}
            />
          </div>
        )}

        <MultipleFormRows
          textButton
          addRowLabel={activityLinkLabel}
          fields={activityRows}
          formRowComponent={ActivityLinkFormRow}
          onAddRow={handleAddActivityRow}
          isAddingRow={isAddingActivityRow}
          path={activitiesPath}
          hideAddRow={activityRows.length === 0}
        />
        <MultipleFormRows
          textButton
          addRowLabel={allocateDowntimeLabel}
          fields={downTimeRows}
          formRowComponent={DownTimeLinkFormRow}
          onAddRow={handleAddDownTimeRow}
          isAddingRow={isAddingDownTimeRow}
          path={downTimesPath}
          hideAddRow={downTimeRows.length === 0}
        />
      </FormBlock>
    </div>
  );
};

export const ShiftReportFormMaterial = () => {
  const messages = useMessageGetter('shiftReport.form');
  const { fieldRows, handleAddRow, isAddingRow } = useShiftReportRows(pathname);

  return (
    <fieldset className="grid grid-cols-1 gap-6">
      <legend>
        <FormSectionHeader>{messages('material')}</FormSectionHeader>
      </legend>
      <MultipleFormRows
        addRowLabel={messages('addMaterial')}
        showEmptyState
        icon={<ShoppingCartIcon className="w-12 h-12 lg:w-14 lg:h-14" />}
        emptyDescription={messages('materialDescription')}
        emptySubDescription={messages('materialSubDescription')}
        fields={fieldRows}
        formRowComponent={MaterialFormRow}
        onAddRow={handleAddRow}
        isAddingRow={isAddingRow}
        path={pathname}
      />
    </fieldset>
  );
};
