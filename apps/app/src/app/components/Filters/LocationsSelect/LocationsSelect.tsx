import { useMessageGetter } from '@messageformat/react';
import type { LocationSchema, ProjectSchema } from '@shape-construction/api/src/types';
import {
  But<PERSON>,
  Drawer,
  InputAdornment,
  SearchField,
  Select,
  type SelectRootProps,
  Tree,
  TreeItem,
  TreeItemContent,
} from '@shape-construction/arch-ui';
import { SkeletonText } from '@shape-construction/arch-ui/src/Skeleton';
import { createItem } from '@shape-construction/arch-ui/src/Tree/tree-utils';
import { useModal } from '@shape-construction/hooks';
import { NoSearchResults } from 'app/components/Search/NoSearchResults';
import { rootId } from 'app/components/Utils/locations';
import { useProjectLocationsTree } from 'app/pages/projects/[projectId]/settings/locations/hooks/useProjectLocationsTree';
import React, { forwardRef, useEffect, useState } from 'react';
import { useParams } from 'react-router';

type Params = { projectId: ProjectSchema['id'] };
type LocationsSelectProps = SelectRootProps<LocationSchema['id'][], true> & {
  triggerLabel: string;
  drawerTitle: string;
  searchPlaceholder: string;
  noLocationLabel: string;
};

const LocationSelectLoading = () => (
  <div className="flex flex-col gap-4 px-2">
    <SkeletonText size="md" animation="pulse" />
    <SkeletonText size="md" animation="pulse" />
    <SkeletonText size="md" animation="pulse" />
  </div>
);

const initialValue: string[] = [];

export const LocationsSelect = forwardRef<React.ElementRef<typeof Select.Root>, LocationsSelectProps>(
  (
    { value = initialValue, onChange, drawerTitle, triggerLabel, searchPlaceholder, noLocationLabel, ...props },
    ref
  ) => {
    const { projectId } = useParams() as Params;
    const actionsMessage = useMessageGetter('actions');
    const { open, openModal, closeModal } = useModal(false);
    const [locationTerm, setLocationTerm] = useState('');
    const [checked, setChecked] = useState(value);
    const { locationsTree, isLoading } = useProjectLocationsTree(
      projectId,
      {
        include_parents: true,
        search: locationTerm,
      },
      {
        enabled: open,
      }
    );

    const locationsList =
      locationTerm.length > 0
        ? locationsTree
        : [
            createItem({
              id: '',
              text: noLocationLabel,
              parent: rootId,
              data: {
                name: noLocationLabel,
              },
            }),
            ...locationsTree,
          ];

    const renderLocations = () => {
      if (isLoading) return <LocationSelectLoading />;
      if (locationsTree.length === 0) return <NoSearchResults />;

      return (
        <Tree
          initialOpen
          rootId="root"
          data={locationsList}
          enableDrag={false}
          checkedOptions={checked}
          onCheck={(values) => setChecked(values as LocationSchema['id'][])}
          render={(node, params) => {
            return (
              <TreeItem aria-label={node.data?.name} node={node} params={params}>
                <TreeItemContent>
                  <p className="truncate" title={node.data?.name}>
                    {node.data?.name}
                  </p>
                </TreeItemContent>
              </TreeItem>
            );
          }}
        />
      );
    };

    useEffect(() => {
      if (value) setChecked(value);
    }, [open, value]);

    useEffect(() => {
      if (!open) setLocationTerm('');
    }, [open]);

    return (
      <Select.Root ref={ref} value={checked} onChange={setChecked} multiple {...props}>
        <Select.Trigger
          as="div"
          variant="bordered"
          size="sm"
          onClick={openModal}
          startAdornment={<InputAdornment>{triggerLabel}</InputAdornment>}
        >
          <div className="text-right">
            <Select.MultipleValue value={value} condensed />
          </div>
        </Select.Trigger>
        <Drawer.Root open={open} onClose={closeModal}>
          <Drawer.Header onClose={closeModal}>
            <Drawer.Title>{drawerTitle}</Drawer.Title>
          </Drawer.Header>
          <Drawer.Content>
            <div className="px-4 pt-2">
              <SearchField placeholder={searchPlaceholder} onChange={(event) => setLocationTerm(event.target.value)} />
            </div>
            <div className="h-full py-4 px-2">{renderLocations()}</div>
          </Drawer.Content>
          <Drawer.Footer>
            <div className="flex gap-x-6 text-sm font-medium leading-5">
              <Button color="secondary" variant="outlined" size="sm" onClick={closeModal}>
                {actionsMessage('cancel')}
              </Button>
              <Button
                color="primary"
                variant="contained"
                size="sm"
                onClick={() => {
                  onChange?.(checked);
                  closeModal();
                }}
              >
                {actionsMessage('save')}
              </Button>
            </div>
          </Drawer.Footer>
        </Drawer.Root>
      </Select.Root>
    );
  }
);
