import { useMessageGetter } from '@messageformat/react';
import type { DisciplineSchema, ProjectSchema } from '@shape-construction/api/src/types';
import {
  <PERSON><PERSON>,
  Drawer,
  InputAdornment,
  SearchField,
  Select,
  type SelectRootProps,
  Tree,
  TreeItem,
  TreeItemContent,
} from '@shape-construction/arch-ui';
import { SkeletonText } from '@shape-construction/arch-ui/src/Skeleton';
import { createItem } from '@shape-construction/arch-ui/src/Tree/tree-utils';
import { useModal } from '@shape-construction/hooks';
import { NoSearchResults } from 'app/components/Search/NoSearchResults';
import { rootId } from 'app/components/Utils/disciplines';
import { useProjectDisciplinesTree } from 'app/pages/projects/[projectId]/settings/disciplines/hooks/useProjectDisciplinesTree';
import React, { forwardRef, useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router';

type Params = { projectId: ProjectSchema['id'] };
type DisciplinesSelectProps = SelectRootProps<DisciplineSchema['id'][], true> & {
  triggerLabel: string;
  drawerTitle: string;
  searchPlaceholder: string;
  noDisciplineLabel: string;
};

const DisciplineSelectLoading = () => (
  <div className="flex flex-col gap-4 px-2">
    <SkeletonText size="md" animation="pulse" />
    <SkeletonText size="md" animation="pulse" />
    <SkeletonText size="md" animation="pulse" />
  </div>
);

const initialValue: string[] = [];

export const DisciplinesSelect = forwardRef<React.ElementRef<typeof Select.Root>, DisciplinesSelectProps>(
  (
    { value = initialValue, onChange, drawerTitle, triggerLabel, searchPlaceholder, noDisciplineLabel, ...props },
    ref
  ) => {
    const { projectId } = useParams() as Params;
    const actionsMessage = useMessageGetter('actions');
    const { open, openModal, closeModal } = useModal(false);
    const [disciplineTerm, setDisciplineTerm] = useState('');
    const [checked, setChecked] = useState(value);
    const { disciplinesTree, isLoading } = useProjectDisciplinesTree(
      projectId,
      {
        include_parents: true,
        search: disciplineTerm,
      },
      {
        enabled: open,
      }
    );

    const disciplinesList = useMemo(
      () =>
        disciplineTerm.length > 0
          ? disciplinesTree
          : [
              createItem({
                id: '',
                text: noDisciplineLabel,
                parent: rootId,
                data: {
                  name: noDisciplineLabel,
                  shortCode: noDisciplineLabel,
                },
              }),
              ...disciplinesTree,
            ],
      [disciplinesTree, disciplineTerm]
    );

    const renderDisciplines = () => {
      if (isLoading) return <DisciplineSelectLoading />;
      if (disciplinesTree.length === 0) return <NoSearchResults />;

      return (
        <Tree
          initialOpen
          rootId="root"
          data={disciplinesList}
          enableDrag={false}
          checkedOptions={checked}
          onCheck={(values) => setChecked(values as DisciplineSchema['id'][])}
          render={(node, params) => {
            return (
              <TreeItem aria-label={node.data?.name} node={node} params={params}>
                <TreeItemContent>
                  <p className="truncate" title={node.data?.name}>
                    {node.data?.name}
                  </p>
                </TreeItemContent>
              </TreeItem>
            );
          }}
        />
      );
    };

    useEffect(() => {
      if (value) setChecked(value);
    }, [open, value]);

    useEffect(() => {
      if (!open) setDisciplineTerm('');
    }, [open]);

    return (
      <Select.Root ref={ref} value={checked} onChange={setChecked} multiple {...props}>
        <Select.Trigger
          as="div"
          variant="bordered"
          size="sm"
          onClick={openModal}
          startAdornment={<InputAdornment>{triggerLabel}</InputAdornment>}
        >
          <div className="text-right">
            <Select.MultipleValue value={value} condensed />
          </div>
        </Select.Trigger>
        <Drawer.Root open={open} onClose={closeModal}>
          <Drawer.Header onClose={closeModal}>
            <Drawer.Title>{drawerTitle}</Drawer.Title>
          </Drawer.Header>
          <Drawer.Content>
            <div className="px-4 pt-2">
              <SearchField
                placeholder={searchPlaceholder}
                onChange={(event) => setDisciplineTerm(event.target.value)}
              />
            </div>
            <div className="h-full py-4 px-2">{renderDisciplines()}</div>
          </Drawer.Content>
          <Drawer.Footer>
            <div className="flex gap-x-6 text-sm font-medium leading-5">
              <Button color="secondary" variant="outlined" size="sm" onClick={closeModal}>
                {actionsMessage('cancel')}
              </Button>
              <Button
                color="primary"
                variant="contained"
                size="sm"
                onClick={() => {
                  onChange?.(checked);
                  closeModal();
                }}
              >
                {actionsMessage('save')}
              </Button>
            </div>
          </Drawer.Footer>
        </Drawer.Root>
      </Select.Root>
    );
  }
);
