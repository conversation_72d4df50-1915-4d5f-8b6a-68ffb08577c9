import { useMessageGetter } from '@messageformat/react';
import type { TeamMemberSchema } from '@shape-construction/api/src/types';
import { InputSelect } from '@shape-construction/arch-ui';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import { OverlayWithForm } from '@shape-construction/arch-ui/src/Overlays';
import { useQuery } from '@tanstack/react-query';
import { useUpdateTeamMember } from 'app/queries/projects/people';
import { getProjectTeamMemberRoleQueryOptions } from 'app/queries/projects/projects';
import { Field } from 'formik';
import React, { useMemo } from 'react';
import * as Yup from 'yup';
import { PersonItem } from '../../PersonItem/PersonItem';
import { useConstructionRoles } from '../constructionRoles/hooks/useConstructionRoles';
import { generateRoleOptions } from '../utils';

export type EditPersonProps = {
  open: boolean;
  person: TeamMemberSchema;
  onClose: () => void;
};

export const EditPerson: React.FC<EditPersonProps> = ({ open, onClose, person }) => {
  const messageGetter = useMessageGetter('admin.people.edit');
  const { constructionRoleOptions, isLoading: isLoadingRoles } = useConstructionRoles();
  const { data: currentProjectRole } = useQuery(getProjectTeamMemberRoleQueryOptions(person.projectId));
  const updateTeamMember = useUpdateTeamMember();
  const roleOptions = useMemo(() => generateRoleOptions(currentProjectRole), [currentProjectRole]);

  const handleSubmit = (values: any) => {
    if (!person) return;
    updateTeamMember.mutate({
      projectId: person.projectId,
      teamId: person.team.id,
      teamMemberId: person.id,
      data: values,
    });
    onClose();
  };

  const formProps = {
    initialValues: {
      constructionRole: person?.constructionRole,
      role: person?.role,
    },
    onSubmit: handleSubmit,
    validationSchema: Yup.object().shape({
      role: Yup.string().required(),
    }),
  };

  if (!person || isLoadingRoles) return null;

  return (
    <OverlayWithForm open={open} formProps={formProps} closeOverlay={onClose} title={messageGetter('title')}>
      <section className="pb-3">
        <PersonItem
          avatar={<Avatar imgURL={person.user.avatarUrl} text={person.user.name} size="lg" />}
          primaryLabel={person.user.name}
          secondaryLabel={person.user.email}
        />
      </section>
      <Field name="role">
        {({ field }: any) => <InputSelect options={roleOptions} label={messageGetter('labels.role')} {...field} />}
      </Field>
      <Field name="constructionRole">
        {({ field }: any) => (
          <InputSelect label={messageGetter('labels.constructionRole')} options={constructionRoleOptions} {...field} />
        )}
      </Field>
    </OverlayWithForm>
  );
};
